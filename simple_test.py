# -*- coding: utf-8 -*-
"""
اختبار بسيط للتطبيق
Simple Test for the Application
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_tkinter():
    """اختبار مكتبة tkinter"""
    try:
        print("🧪 اختبار مكتبة tkinter...")
        
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.title("اختبار tkinter")
        root.geometry("400x200")
        
        # إضافة تسمية
        label = tk.Label(
            root,
            text="✅ مكتبة tkinter تعمل بشكل صحيح!",
            font=('Arial', 14),
            fg='green'
        )
        label.pack(pady=50)
        
        # زر الإغلاق
        close_btn = tk.Button(
            root,
            text="إغلاق",
            command=root.destroy,
            font=('Arial', 12),
            bg='lightblue'
        )
        close_btn.pack(pady=10)
        
        print("✅ تم إنشاء نافذة tkinter بنجاح")
        print("🔍 يرجى إغلاق النافذة للمتابعة...")
        
        # عرض النافذة
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار tkinter: {e}")
        return False

def test_imports():
    """اختبار استيراد الملفات"""
    try:
        print("\n📦 اختبار استيراد الملفات...")
        
        # اختبار استيراد config
        try:
            from config import COLORS, FONTS, SCHOOL_SETTINGS
            print("✅ تم استيراد config.py بنجاح")
        except Exception as e:
            print(f"❌ خطأ في استيراد config.py: {e}")
            return False
        
        # اختبار استيراد simple_app
        try:
            from simple_app import SimpleSchoolFinanceApp
            print("✅ تم استيراد simple_app.py بنجاح")
        except Exception as e:
            print(f"❌ خطأ في استيراد simple_app.py: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في الاستيراد: {e}")
        return False

def test_simple_app():
    """اختبار التطبيق المبسط"""
    try:
        print("\n🚀 اختبار التطبيق المبسط...")
        
        # استيراد التطبيق
        from simple_app import SimpleSchoolFinanceApp
        
        print("✅ تم استيراد التطبيق بنجاح")
        print("🔍 سيتم فتح التطبيق - يرجى إغلاقه للمتابعة...")
        
        # إنشاء وتشغيل التطبيق
        app = SimpleSchoolFinanceApp()
        
        # إضافة رسالة ترحيب
        messagebox.showinfo(
            "مرحباً",
            "🎉 مرحباً بك في نظام إدارة البيانات المالية للمدرسة!\n\n"
            "هذا اختبار للتأكد من عمل التطبيق بشكل صحيح.\n"
            "يمكنك استكشاف الواجهة ثم إغلاق التطبيق."
        )
        
        app.run()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التطبيق: {e}")
        return False

def check_files():
    """فحص وجود الملفات المطلوبة"""
    print("\n📁 فحص الملفات المطلوبة...")
    
    required_files = [
        'config.py',
        'simple_app.py',
        'main.py',
        'database.py',
        'ui_components.py',
        'data_manager.py',
        'year_transfer.py',
        'requirements.txt',
        'README.md'
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - مفقود")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️ الملفات المفقودة: {', '.join(missing_files)}")
        return False
    else:
        print("\n✅ جميع الملفات موجودة")
        return True

def print_system_info():
    """طباعة معلومات النظام"""
    print("💻 معلومات النظام:")
    print(f"Python Version: {sys.version}")
    print(f"Platform: {sys.platform}")
    print(f"Current Directory: {os.getcwd()}")

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل للتطبيق")
    print("=" * 40)
    
    # طباعة معلومات النظام
    print_system_info()
    
    # فحص الملفات
    if not check_files():
        print("\n❌ بعض الملفات مفقودة!")
        return
    
    # اختبار الاستيراد
    if not test_imports():
        print("\n❌ فشل في اختبار الاستيراد!")
        return
    
    # اختبار tkinter
    print("\n" + "=" * 40)
    print("سيتم فتح نافذة اختبار tkinter...")
    input("اضغط Enter للمتابعة...")
    
    if not test_tkinter():
        print("\n❌ فشل في اختبار tkinter!")
        return
    
    # اختبار التطبيق
    print("\n" + "=" * 40)
    print("سيتم فتح التطبيق الكامل...")
    choice = input("هل تريد فتح التطبيق؟ (y/n): ").lower().strip()
    
    if choice in ['y', 'yes', 'نعم', '']:
        if test_simple_app():
            print("\n✅ نجح اختبار التطبيق!")
        else:
            print("\n❌ فشل في اختبار التطبيق!")
    
    print("\n" + "=" * 40)
    print("🎉 انتهى الاختبار!")
    print("\nالخطوات التالية:")
    print("1. لتشغيل التطبيق: python simple_app.py")
    print("2. لتثبيت المكتبات الإضافية: pip install supabase pandas openpyxl")
    print("3. لتشغيل النسخة الكاملة: python main.py")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
