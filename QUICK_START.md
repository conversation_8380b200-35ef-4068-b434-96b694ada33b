# 🚀 دليل البدء السريع

## 🎯 أسرع طريقة للتشغيل

### الطريقة الأولى: التشغيل المباشر
```bash
python simple_app.py
```

### الطريقة الثانية: استخدام المشغل
```bash
python launcher.py
```

### الطريقة الثالثة: ملف التشغيل
```bash
# Windows
run.bat

# Linux/Mac
./run.sh
```

## 📋 متطلبات النظام

### الحد الأدنى
- Python 3.8 أو أحدث
- tkinter (مدمج مع Python)

### للميزات المتقدمة
```bash
pip install supabase pandas openpyxl
```

## 🎮 كيفية الاستخدام

### 1. البدء
- شغل التطبيق بأي من الطرق أعلاه
- ستظهر واجهة المستخدم مع بيانات تجريبية

### 2. استكشاف البيانات
- استخدم مربع البحث للبحث عن طالب
- اختر المرحلة من القائمة المنسدلة
- انقر على "مسح" لإزالة الفلاتر

### 3. عرض الإحصائيات
- راجع الإحصائيات في الشريط الجانبي
- إجمالي الطلاب والرسوم
- نسبة التحصيل

### 4. العمليات الأساسية
- **➕ إضافة طالب**: إضافة طالب جديد
- **📥 استيراد بيانات**: من ملف Excel
- **📤 تصدير بيانات**: إلى ملف Excel
- **🔄 نقل سنة دراسية**: نقل البيانات للسنة الجديدة

## 🔧 حل المشاكل الشائعة

### المشكلة: "No module named 'tkinter'"
**الحل:**
```bash
# Ubuntu/Debian
sudo apt-get install python3-tk

# CentOS/RHEL
sudo yum install tkinter
```

### المشكلة: "No module named 'supabase'"
**الحل:**
```bash
pip install supabase
# أو استخدم النسخة المبسطة
python simple_app.py
```

### المشكلة: النافذة لا تظهر
**الحل:**
- تأكد من أن Python مثبت بشكل صحيح
- جرب تشغيل `python --version`
- استخدم `python3` بدلاً من `python`

## 📱 واجهة المستخدم

### الشريط الجانبي
- معلومات السنة الدراسية
- إحصائيات سريعة
- أزرار العمليات الرئيسية

### المنطقة الرئيسية
- شريط البحث والتصفية
- جدول البيانات
- شريط الحالة

### الألوان والرموز
- 🟢 أخضر: نجح
- 🟡 أصفر: تحذير
- 🔴 أحمر: خطأ
- 🔵 أزرق: معلومات

## 💡 نصائح مفيدة

### للمبتدئين
1. ابدأ بالنسخة المبسطة
2. استكشف البيانات التجريبية
3. جرب البحث والتصفية
4. راجع الإحصائيات

### للمستخدمين المتقدمين
1. ثبت المتطلبات الكاملة
2. اربط قاعدة بيانات Supabase
3. استورد بياناتك الحقيقية
4. استخدم ميزة نقل السنوات

## 🎯 البيانات التجريبية

النظام يأتي مع 5 طلاب تجريبيين:
- أحمد محمد علي (ابتدائي)
- فاطمة أحمد حسن (إعدادي)
- محمد عبد الله (ثانوي)
- نور الهدى سالم (ابتدائي)
- يوسف إبراهيم (إعدادي)

## 📞 الحصول على المساعدة

### إذا واجهت مشكلة:
1. راجع هذا الدليل
2. اقرأ ملف README.md
3. جرب ملف simple_test.py
4. تواصل مع الدعم الفني

### ملفات مفيدة:
- `README.md` - دليل شامل
- `PROJECT_SUMMARY.md` - ملخص المشروع
- `simple_test.py` - اختبار النظام

---

## 🎉 مبروك!

أنت الآن جاهز لاستخدام نظام إدارة البيانات المالية للمدرسة!

**استمتع بالتجربة! 🏫✨**
