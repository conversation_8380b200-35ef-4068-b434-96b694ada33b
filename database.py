# -*- coding: utf-8 -*-
"""
إدارة قاعدة البيانات - Supabase
School Financial Management System - Database Manager
"""

import asyncio
from typing import Dict, List, Optional, Any
from supabase import create_client, Client
from datetime import datetime, date
import json
import logging
from config import SUPABASE_CONFIG, MESSAGES

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """مدير قاعدة البيانات الرئيسي"""
    
    def __init__(self):
        """تهيئة الاتصال بقاعدة البيانات"""
        try:
            self.supabase: Client = create_client(
                SUPABASE_CONFIG['url'],
                SUPABASE_CONFIG['anon_key']
            )
            self.service_client: Client = create_client(
                SUPABASE_CONFIG['url'],
                SUPABASE_CONFIG['service_key']
            )
            logger.info("✅ تم الاتصال بقاعدة البيانات بنجاح")
        except Exception as e:
            logger.error(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
            raise

    async def initialize_database(self):
        """إنشاء الجداول الأساسية"""
        try:
            # إنشاء جدول الطلاب
            await self._create_students_table()
            # إنشاء جدول هيكل الرسوم
            await self._create_fees_structure_table()
            # إنشاء جدول المدفوعات
            await self._create_payments_table()
            # إنشاء جدول الخصومات
            await self._create_discounts_table()
            # إنشاء جدول السنوات الدراسية
            await self._create_academic_years_table()
            
            logger.info("✅ تم إنشاء جميع الجداول بنجاح")
            return True
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء الجداول: {e}")
            return False

    async def _create_students_table(self):
        """إنشاء جدول الطلاب"""
        # سيتم إنشاء الجدول عبر Supabase Dashboard أو SQL
        pass

    async def _create_fees_structure_table(self):
        """إنشاء جدول هيكل الرسوم"""
        pass

    async def _create_payments_table(self):
        """إنشاء جدول المدفوعات"""
        pass

    async def _create_discounts_table(self):
        """إنشاء جدول الخصومات"""
        pass

    async def _create_academic_years_table(self):
        """إنشاء جدول السنوات الدراسية"""
        pass

    # 👨‍🎓 عمليات الطلاب
    def get_all_students(self, academic_year: str = None) -> List[Dict]:
        """الحصول على جميع الطلاب"""
        try:
            query = self.supabase.table('students').select('*')
            if academic_year:
                query = query.eq('academic_year', academic_year)
            
            result = query.execute()
            return result.data if result.data else []
        except Exception as e:
            logger.error(f"خطأ في جلب بيانات الطلاب: {e}")
            return []

    def get_student_by_id(self, student_id: int) -> Optional[Dict]:
        """الحصول على طالب بالرقم التسلسلي"""
        try:
            result = self.supabase.table('students').select('*').eq('id', student_id).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"خطأ في جلب بيانات الطالب: {e}")
            return None

    def add_student(self, student_data: Dict) -> bool:
        """إضافة طالب جديد"""
        try:
            # إضافة التاريخ الحالي
            student_data['created_at'] = datetime.now().isoformat()
            student_data['updated_at'] = datetime.now().isoformat()
            
            result = self.supabase.table('students').insert(student_data).execute()
            if result.data:
                logger.info(f"✅ تم إضافة الطالب: {student_data.get('name', 'غير محدد')}")
                return True
            return False
        except Exception as e:
            logger.error(f"خطأ في إضافة الطالب: {e}")
            return False

    def update_student(self, student_id: int, student_data: Dict) -> bool:
        """تحديث بيانات طالب"""
        try:
            student_data['updated_at'] = datetime.now().isoformat()
            
            result = self.supabase.table('students').update(student_data).eq('id', student_id).execute()
            if result.data:
                logger.info(f"✅ تم تحديث بيانات الطالب رقم: {student_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"خطأ في تحديث الطالب: {e}")
            return False

    def delete_student(self, student_id: int) -> bool:
        """حذف طالب"""
        try:
            result = self.supabase.table('students').delete().eq('id', student_id).execute()
            if result.data:
                logger.info(f"✅ تم حذف الطالب رقم: {student_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"خطأ في حذف الطالب: {e}")
            return False

    # 💰 عمليات المدفوعات
    def add_payment(self, payment_data: Dict) -> bool:
        """إضافة دفعة جديدة"""
        try:
            payment_data['created_at'] = datetime.now().isoformat()
            
            result = self.supabase.table('payments').insert(payment_data).execute()
            if result.data:
                logger.info(f"✅ تم تسجيل دفعة بمبلغ: {payment_data.get('amount', 0)}")
                return True
            return False
        except Exception as e:
            logger.error(f"خطأ في تسجيل الدفعة: {e}")
            return False

    def get_student_payments(self, student_id: int) -> List[Dict]:
        """الحصول على مدفوعات طالب"""
        try:
            result = self.supabase.table('payments').select('*').eq('student_id', student_id).execute()
            return result.data if result.data else []
        except Exception as e:
            logger.error(f"خطأ في جلب المدفوعات: {e}")
            return []

    # 🏷️ عمليات الخصومات
    def add_discount(self, discount_data: Dict) -> bool:
        """إضافة خصم جديد"""
        try:
            discount_data['created_at'] = datetime.now().isoformat()
            
            result = self.supabase.table('discounts').insert(discount_data).execute()
            if result.data:
                logger.info(f"✅ تم إضافة خصم: {discount_data.get('type', 'غير محدد')}")
                return True
            return False
        except Exception as e:
            logger.error(f"خطأ في إضافة الخصم: {e}")
            return False

    def get_student_discounts(self, student_id: int) -> List[Dict]:
        """الحصول على خصومات طالب"""
        try:
            result = self.supabase.table('discounts').select('*').eq('student_id', student_id).execute()
            return result.data if result.data else []
        except Exception as e:
            logger.error(f"خطأ في جلب الخصومات: {e}")
            return []

    # 📊 التقارير والإحصائيات
    def get_financial_summary(self, academic_year: str = None) -> Dict:
        """الحصول على ملخص مالي"""
        try:
            students = self.get_all_students(academic_year)
            
            total_students = len(students)
            total_fees = sum(float(s.get('total_fees', 0)) for s in students)
            total_paid = sum(float(s.get('total_paid', 0)) for s in students)
            total_remaining = total_fees - total_paid
            total_discounts = sum(float(s.get('total_discounts', 0)) for s in students)
            
            return {
                'total_students': total_students,
                'total_fees': total_fees,
                'total_paid': total_paid,
                'total_remaining': total_remaining,
                'total_discounts': total_discounts,
                'collection_rate': (total_paid / total_fees * 100) if total_fees > 0 else 0
            }
        except Exception as e:
            logger.error(f"خطأ في حساب الملخص المالي: {e}")
            return {}

    # 🔍 البحث والتصفية
    def search_students(self, search_term: str, filters: Dict = None) -> List[Dict]:
        """البحث في الطلاب"""
        try:
            query = self.supabase.table('students').select('*')
            
            # البحث في الاسم
            if search_term:
                query = query.ilike('name', f'%{search_term}%')
            
            # تطبيق الفلاتر
            if filters:
                if filters.get('stage'):
                    query = query.eq('stage', filters['stage'])
                if filters.get('class'):
                    query = query.eq('class', filters['class'])
                if filters.get('academic_year'):
                    query = query.eq('academic_year', filters['academic_year'])
            
            result = query.execute()
            return result.data if result.data else []
        except Exception as e:
            logger.error(f"خطأ في البحث: {e}")
            return []

    # 🔄 نقل البيانات بين السنوات
    def transfer_to_new_year(self, source_year: str, target_year: str, options: Dict) -> bool:
        """نقل البيانات لسنة دراسية جديدة"""
        try:
            students = self.get_all_students(source_year)
            
            for student in students:
                new_student_data = self._prepare_student_for_new_year(student, target_year, options)
                self.add_student(new_student_data)
            
            logger.info(f"✅ تم نقل {len(students)} طالب للسنة {target_year}")
            return True
        except Exception as e:
            logger.error(f"خطأ في نقل البيانات: {e}")
            return False

    def _prepare_student_for_new_year(self, student: Dict, target_year: str, options: Dict) -> Dict:
        """تحضير بيانات الطالب للسنة الجديدة"""
        new_student = student.copy()
        
        # تحديث السنة الدراسية
        new_student['academic_year'] = target_year
        
        # تحديث المرحلة الدراسية
        new_student = self._update_student_stage(new_student)
        
        # معالجة المديونيات
        if options.get('transfer_debts', True):
            new_student['previous_debt'] = student.get('remaining_amount', 0)
        else:
            new_student['previous_debt'] = 0
        
        # إعادة تصفير المدفوعات
        if options.get('reset_payments', True):
            for i in range(1, 10):  # 9 أقساط
                new_student[f'installment_{i}_amount'] = 0
                new_student[f'installment_{i}_date'] = None
                new_student[f'installment_{i}_receipt'] = None
        
        # تحديث الرسوم
        if options.get('update_fees', True):
            new_student = self._update_student_fees(new_student)
        
        # إزالة المعرف للإدراج كسجل جديد
        new_student.pop('id', None)
        new_student.pop('created_at', None)
        new_student.pop('updated_at', None)
        
        return new_student

    def _update_student_stage(self, student: Dict) -> Dict:
        """تحديث المرحلة الدراسية للطالب"""
        current_stage = student.get('stage', '')
        current_class = student.get('class', '')
        
        # منطق ترقية المراحل
        stage_progression = {
            'ابتدائي': {
                'السادس': {'stage': 'إعدادي', 'class': 'الأول'}
            },
            'إعدادي': {
                'الثالث': {'stage': 'ثانوي', 'class': 'الأول'}
            }
        }
        
        if current_stage in stage_progression and current_class in stage_progression[current_stage]:
            progression = stage_progression[current_stage][current_class]
            student['stage'] = progression['stage']
            student['class'] = progression['class']
        else:
            # ترقية عادية داخل نفس المرحلة
            class_order = ['الأول', 'الثاني', 'الثالث', 'الرابع', 'الخامس', 'السادس']
            if current_class in class_order:
                current_index = class_order.index(current_class)
                if current_index < len(class_order) - 1:
                    student['class'] = class_order[current_index + 1]
        
        return student

    def _update_student_fees(self, student: Dict) -> Dict:
        """تحديث رسوم الطالب حسب المرحلة الجديدة"""
        from config import DEFAULT_FEES
        
        stage = student.get('stage', '')
        if stage in DEFAULT_FEES:
            fees_structure = DEFAULT_FEES[stage]
            student['tuition_fees'] = fees_structure['tuition']
            student['file_opening_fee'] = fees_structure['file_opening']
            student['transfer_file_fee'] = fees_structure['transfer_file']
            
            # حساب إجمالي الرسوم
            total_fees = (
                fees_structure['tuition'] + 
                fees_structure['file_opening'] + 
                fees_structure['transfer_file']
            )
            student['total_fees'] = total_fees
        
        return student

    # 💾 النسخ الاحتياطي والاستعادة
    def create_backup(self) -> str:
        """إنشاء نسخة احتياطية"""
        try:
            backup_data = {
                'students': self.get_all_students(),
                'timestamp': datetime.now().isoformat(),
                'version': '1.0'
            }
            
            filename = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            filepath = f"backups/{filename}"
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ تم إنشاء نسخة احتياطية: {filename}")
            return filepath
        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return ""

    def test_connection(self) -> bool:
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            # محاولة جلب بيانات بسيطة
            result = self.supabase.table('students').select('id').limit(1).execute()
            return True
        except Exception as e:
            logger.error(f"فشل اختبار الاتصال: {e}")
            return False
