#!/bin/bash

echo "🏫 نظام إدارة البيانات المالية للمدرسة"
echo "====================================="
echo ""
echo "اختر نوع التشغيل:"
echo "1. التشغيل السريع (النسخة المبسطة)"
echo "2. التشغيل الكامل (مع جميع الميزات)"
echo "3. تثبيت المتطلبات"
echo "4. خروج"
echo ""

read -p "اختر رقم (1-4): " choice

case $choice in
    1)
        echo ""
        echo "🚀 تشغيل النسخة المبسطة..."
        python3 simple_app.py
        ;;
    2)
        echo ""
        echo "🚀 تشغيل النسخة الكاملة..."
        python3 main.py
        ;;
    3)
        echo ""
        echo "📦 تثبيت المتطلبات..."
        pip3 install -r requirements.txt
        echo ""
        echo "✅ تم تثبيت المتطلبات بنجاح!"
        ;;
    4)
        echo ""
        echo "👋 وداعاً!"
        exit 0
        ;;
    *)
        echo ""
        echo "❌ اختيار غير صحيح!"
        ;;
esac

echo ""
echo "اضغط Enter للمتابعة..."
read
