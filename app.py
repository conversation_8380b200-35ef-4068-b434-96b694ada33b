import streamlit as st
import pandas as pd
import numpy as np
from supabase import create_client
import os
from dotenv import load_dotenv
from difflib import SequenceMatcher
import json

# Load environment variables
load_dotenv()

# Initialize Supabase client
supabase = create_client(
    os.getenv("SUPABASE_URL"),
    os.getenv("SUPABASE_KEY")
)

def similar(a, b):
    """Calculate string similarity ratio"""
    return SequenceMatcher(None, a, b).ratio()

def map_columns(old_columns, new_columns, threshold=0.6):
    """Map old columns to new columns based on similarity"""
    mapping = {}
    for old_col in old_columns:
        best_match = None
        best_score = threshold
        for new_col in new_columns:
            score = similar(old_col.lower(), new_col.lower())
            if score > best_score:
                best_score = score
                best_match = new_col
        if best_match:
            mapping[old_col] = best_match
    return mapping

def calculate_balance(row, new_fees):
    """Calculate remaining balance and new year fees"""
    old_paid = row.get('paid_amount', 0)
    old_total = row.get('total_fees', 0)
    old_balance = old_total - old_paid
    
    # Get new fees based on grade
    grade = row.get('grade', '')
    new_total = new_fees.get(grade, 0)
    
    return {
        'old_balance': old_balance,
        'new_total': new_total,
        'total_balance': old_balance + new_total
    }

def main():
    st.set_page_config(layout="wide", page_title="Student Financial Management System")
    st.title("نظام إدارة الرسوم المالية للطلاب")
    
    # File upload section
    st.header("استيراد البيانات")
    uploaded_file = st.file_uploader("اختر ملف Excel للعام السابق", type=['xlsx', 'xls'])
    
    if uploaded_file is not None:
        # Read the old Excel file
        df_old = pd.read_excel(uploaded_file)
        
        # Get column mapping
        st.subheader("ربط الأعمدة")
        old_columns = df_old.columns.tolist()
        new_columns = [
            'student_id', 'name', 'grade', 'total_fees', 
            'paid_amount', 'remaining_balance', 'notes'
        ]
        
        # Automatic column mapping
        auto_mapping = map_columns(old_columns, new_columns)
        
        # Manual column mapping interface
        st.write("يمكنك تعديل الربط التلقائي للأعمدة:")
        column_mapping = {}
        for old_col in old_columns:
            if old_col in auto_mapping:
                default_idx = new_columns.index(auto_mapping[old_col])
            else:
                default_idx = 0
            column_mapping[old_col] = st.selectbox(
                f"اختر العمود المناسب لـ {old_col}",
                new_columns,
                index=default_idx
            )
        
        # Grade fees configuration
        st.subheader("تكوين رسوم المراحل الدراسية")
        grades = df_old[column_mapping['grade']].unique()
        new_fees = {}
        for grade in grades:
            new_fees[grade] = st.number_input(
                f"رسوم {grade} للعام الجديد",
                min_value=0,
                value=0
            )
        
        # Process data
        if st.button("معالجة البيانات"):
            # Create new dataframe with mapped columns
            df_new = pd.DataFrame()
            for old_col, new_col in column_mapping.items():
                df_new[new_col] = df_old[old_col]
            
            # Calculate balances
            df_new['old_balance'] = df_new.apply(
                lambda row: calculate_balance(row, new_fees)['old_balance'],
                axis=1
            )
            df_new['new_total'] = df_new.apply(
                lambda row: calculate_balance(row, new_fees)['new_total'],
                axis=1
            )
            df_new['total_balance'] = df_new.apply(
                lambda row: calculate_balance(row, new_fees)['total_balance'],
                axis=1
            )
            
            # Display results
            st.subheader("نتائج المعالجة")
            st.dataframe(df_new)
            
            # Export options
            st.subheader("تصدير البيانات")
            if st.button("تصدير إلى Excel"):
                df_new.to_excel("processed_data.xlsx", index=False)
                st.success("تم تصدير البيانات بنجاح!")
            
            if st.button("رفع إلى قاعدة البيانات"):
                # Convert DataFrame to records
                records = df_new.to_dict('records')
                # Upload to Supabase
                try:
                    supabase.table('student_fees').insert(records).execute()
                    st.success("تم رفع البيانات إلى قاعدة البيانات بنجاح!")
                except Exception as e:
                    st.error(f"حدث خطأ أثناء رفع البيانات: {str(e)}")

if __name__ == "__main__":
    main() 