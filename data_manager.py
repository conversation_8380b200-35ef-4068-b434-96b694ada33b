# -*- coding: utf-8 -*-
"""
مدير البيانات والاستيراد الذكي
School Financial Management System - Data Manager
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import tkinter as tk
from tkinter import filedialog, messagebox
from fuzzywuzzy import fuzz, process
import openpyxl
from datetime import datetime
import logging
from config import MESSAGES, EMOJIS

logger = logging.getLogger(__name__)

class DataManager:
    """مدير البيانات والاستيراد الذكي"""
    
    def __init__(self, db_manager):
        """تهيئة مدير البيانات"""
        self.db_manager = db_manager
        self.column_mapping = {}
        self.imported_data = None
        
        # الأعمدة المطلوبة في النظام
        self.required_columns = {
            'name': ['اسم الطالب', 'الاسم', 'Student Name', 'Name'],
            'class': ['الفصل', 'الصف', 'Class', 'Grade'],
            'stage': ['المرحلة', 'المرحلة التعليمية', 'Stage', 'Level'],
            'serial_number': ['الرقم التسلسلي', 'رقم الطالب', 'Serial', 'ID'],
            'batch_number': ['رقم الدفعة', 'الدفعة', 'Batch'],
            'total_fees': ['إجمالي الرسوم', 'الرسوم الكلية', 'Total Fees'],
            'total_paid': ['إجمالي المدفوع', 'المدفوع', 'Paid Amount'],
            'remaining_amount': ['المتبقي', 'المبلغ المتبقي', 'Remaining'],
            'notes': ['ملاحظات', 'Notes', 'Comments']
        }
    
    def import_from_excel(self, file_path: str = None) -> bool:
        """استيراد البيانات من ملف Excel"""
        try:
            if not file_path:
                file_path = filedialog.askopenfilename(
                    title="اختر ملف Excel للاستيراد",
                    filetypes=[
                        ("Excel files", "*.xlsx *.xls"),
                        ("CSV files", "*.csv"),
                        ("All files", "*.*")
                    ]
                )
            
            if not file_path:
                return False
            
            # قراءة الملف
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8-sig')
            else:
                df = pd.read_excel(file_path)
            
            if df.empty:
                messagebox.showerror("خطأ", "الملف فارغ أو لا يحتوي على بيانات صالحة")
                return False
            
            # تحليل الأعمدة وإنشاء التطابق
            column_mapping = self._analyze_columns(df.columns.tolist())
            
            # عرض نافذة تأكيد التطابق
            if self._show_column_mapping_dialog(df.columns.tolist(), column_mapping):
                # تطبيق التطابق وتنظيف البيانات
                cleaned_data = self._clean_and_map_data(df, self.column_mapping)
                
                # حفظ البيانات في قاعدة البيانات
                success_count = self._save_imported_data(cleaned_data)
                
                if success_count > 0:
                    messagebox.showinfo(
                        "نجح الاستيراد",
                        f"تم استيراد {success_count} طالب بنجاح!"
                    )
                    return True
                else:
                    messagebox.showerror("خطأ", "فشل في حفظ البيانات")
                    return False
            
            return False
            
        except Exception as e:
            logger.error(f"خطأ في استيراد البيانات: {e}")
            messagebox.showerror("خطأ", f"خطأ في استيراد البيانات: {e}")
            return False
    
    def _analyze_columns(self, file_columns: List[str]) -> Dict[str, str]:
        """تحليل أعمدة الملف وإنشاء تطابق ذكي"""
        mapping = {}
        
        for system_col, possible_names in self.required_columns.items():
            best_match = None
            best_score = 0
            
            for file_col in file_columns:
                for possible_name in possible_names:
                    # استخدام fuzzy matching للعثور على أفضل تطابق
                    score = fuzz.ratio(file_col.lower(), possible_name.lower())
                    if score > best_score and score > 70:  # حد أدنى للتطابق
                        best_score = score
                        best_match = file_col
            
            if best_match:
                mapping[system_col] = best_match
        
        return mapping
    
    def _show_column_mapping_dialog(self, file_columns: List[str], suggested_mapping: Dict[str, str]) -> bool:
        """عرض نافذة تأكيد تطابق الأعمدة"""
        dialog = ColumnMappingDialog(None, file_columns, suggested_mapping, self.required_columns)
        result = dialog.show()
        
        if result:
            self.column_mapping = dialog.get_mapping()
            return True
        
        return False
    
    def _clean_and_map_data(self, df: pd.DataFrame, mapping: Dict[str, str]) -> List[Dict]:
        """تنظيف البيانات وتطبيق التطابق"""
        cleaned_data = []
        
        for index, row in df.iterrows():
            student_data = {}
            
            # تطبيق التطابق
            for system_col, file_col in mapping.items():
                if file_col in df.columns:
                    value = row[file_col]
                    
                    # تنظيف القيم
                    if pd.isna(value):
                        value = ""
                    elif system_col in ['total_fees', 'total_paid', 'remaining_amount']:
                        # تنظيف القيم المالية
                        value = self._clean_financial_value(value)
                    else:
                        value = str(value).strip()
                    
                    student_data[system_col] = value
            
            # إضافة القيم الافتراضية
            student_data.setdefault('academic_year', '2024/2025')
            student_data.setdefault('created_at', datetime.now().isoformat())
            
            # حساب القيم المشتقة
            self._calculate_derived_values(student_data)
            
            cleaned_data.append(student_data)
        
        return cleaned_data
    
    def _clean_financial_value(self, value) -> float:
        """تنظيف القيم المالية"""
        if pd.isna(value):
            return 0.0
        
        # إزالة الرموز والمسافات
        if isinstance(value, str):
            value = value.replace(',', '').replace('$', '').replace('ج.م', '').strip()
            try:
                return float(value)
            except ValueError:
                return 0.0
        
        try:
            return float(value)
        except (ValueError, TypeError):
            return 0.0
    
    def _calculate_derived_values(self, student_data: Dict):
        """حساب القيم المشتقة"""
        total_fees = student_data.get('total_fees', 0)
        total_paid = student_data.get('total_paid', 0)
        
        # حساب المبلغ المتبقي
        if not student_data.get('remaining_amount'):
            student_data['remaining_amount'] = max(0, total_fees - total_paid)
        
        # إضافة قيم افتراضية للخصومات
        student_data.setdefault('total_discounts', 0)
        student_data.setdefault('discount_siblings', 0)
        student_data.setdefault('discount_cash', 0)
        student_data.setdefault('discount_teacher', 0)
        student_data.setdefault('discount_transfer', 0)
        student_data.setdefault('discount_personal', 0)
    
    def _save_imported_data(self, data: List[Dict]) -> int:
        """حفظ البيانات المستوردة في قاعدة البيانات"""
        success_count = 0
        
        for student_data in data:
            try:
                if self.db_manager and self.db_manager.add_student(student_data):
                    success_count += 1
                else:
                    logger.warning(f"فشل في حفظ الطالب: {student_data.get('name', 'غير محدد')}")
            except Exception as e:
                logger.error(f"خطأ في حفظ الطالب: {e}")
        
        return success_count
    
    def export_to_excel(self, data: List[Dict], file_path: str = None) -> bool:
        """تصدير البيانات إلى Excel"""
        try:
            if not file_path:
                file_path = filedialog.asksaveasfilename(
                    title="حفظ البيانات كـ Excel",
                    defaultextension=".xlsx",
                    filetypes=[
                        ("Excel files", "*.xlsx"),
                        ("CSV files", "*.csv"),
                        ("All files", "*.*")
                    ]
                )
            
            if not file_path:
                return False
            
            # تحويل البيانات إلى DataFrame
            df = pd.DataFrame(data)
            
            # إعادة ترتيب الأعمدة
            column_order = [
                'serial_number', 'name', 'class', 'stage', 'batch_number',
                'total_fees', 'total_paid', 'remaining_amount', 'total_discounts',
                'notes'
            ]
            
            # ترتيب الأعمدة الموجودة فقط
            available_columns = [col for col in column_order if col in df.columns]
            df = df[available_columns]
            
            # تصدير حسب نوع الملف
            if file_path.endswith('.csv'):
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
            else:
                # تصدير Excel مع تنسيق
                with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='بيانات الطلاب', index=False)
                    
                    # تنسيق الورقة
                    worksheet = writer.sheets['بيانات الطلاب']
                    self._format_excel_sheet(worksheet, df)
            
            logger.info(f"تم تصدير {len(data)} طالب إلى {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تصدير البيانات: {e}")
            messagebox.showerror("خطأ", f"خطأ في تصدير البيانات: {e}")
            return False
    
    def _format_excel_sheet(self, worksheet, df):
        """تنسيق ورقة Excel"""
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        
        # تنسيق رأس الجدول
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        
        for col_num, column_title in enumerate(df.columns, 1):
            cell = worksheet.cell(row=1, column=col_num)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal="center", vertical="center")
        
        # تنسيق البيانات
        for row_num in range(2, len(df) + 2):
            for col_num in range(1, len(df.columns) + 1):
                cell = worksheet.cell(row=row_num, column=col_num)
                cell.alignment = Alignment(horizontal="center", vertical="center")
        
        # ضبط عرض الأعمدة
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    def validate_data(self, data: List[Dict]) -> Tuple[List[Dict], List[str]]:
        """التحقق من صحة البيانات"""
        valid_data = []
        errors = []
        
        for i, student in enumerate(data, 1):
            student_errors = []
            
            # التحقق من الحقول المطلوبة
            if not student.get('name', '').strip():
                student_errors.append(f"الصف {i}: اسم الطالب مطلوب")
            
            # التحقق من القيم المالية
            try:
                total_fees = float(student.get('total_fees', 0))
                total_paid = float(student.get('total_paid', 0))
                
                if total_fees < 0:
                    student_errors.append(f"الصف {i}: إجمالي الرسوم لا يمكن أن يكون سالباً")
                
                if total_paid < 0:
                    student_errors.append(f"الصف {i}: المبلغ المدفوع لا يمكن أن يكون سالباً")
                
                if total_paid > total_fees:
                    student_errors.append(f"الصف {i}: المبلغ المدفوع أكبر من إجمالي الرسوم")
                    
            except (ValueError, TypeError):
                student_errors.append(f"الصف {i}: قيم مالية غير صحيحة")
            
            if student_errors:
                errors.extend(student_errors)
            else:
                valid_data.append(student)
        
        return valid_data, errors
    
    def get_import_template(self) -> pd.DataFrame:
        """إنشاء قالب للاستيراد"""
        template_data = {
            'الرقم التسلسلي': ['2025001', '2025002'],
            'اسم الطالب': ['أحمد محمد علي', 'فاطمة أحمد حسن'],
            'الفصل': ['الأول', 'الثاني'],
            'المرحلة': ['ابتدائي', 'إعدادي'],
            'رقم الدفعة': ['A001', 'A002'],
            'إجمالي الرسوم': [15000.00, 18000.00],
            'إجمالي المدفوع': [10000.00, 15000.00],
            'المبلغ المتبقي': [5000.00, 3000.00],
            'ملاحظات': ['طالب متفوق', 'خصم الأخوة']
        }
        
        return pd.DataFrame(template_data)
    
    def save_template(self, file_path: str = None) -> bool:
        """حفظ قالب الاستيراد"""
        try:
            if not file_path:
                file_path = filedialog.asksaveasfilename(
                    title="حفظ قالب الاستيراد",
                    defaultextension=".xlsx",
                    filetypes=[("Excel files", "*.xlsx")]
                )
            
            if not file_path:
                return False
            
            template_df = self.get_import_template()
            template_df.to_excel(file_path, index=False)
            
            messagebox.showinfo("تم الحفظ", f"تم حفظ قالب الاستيراد في:\n{file_path}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حفظ القالب: {e}")
            messagebox.showerror("خطأ", f"خطأ في حفظ القالب: {e}")
            return False

class ColumnMappingDialog:
    """نافذة تطابق الأعمدة"""
    
    def __init__(self, parent, file_columns: List[str], suggested_mapping: Dict[str, str], required_columns: Dict[str, List[str]]):
        self.parent = parent
        self.file_columns = file_columns
        self.suggested_mapping = suggested_mapping
        self.required_columns = required_columns
        self.mapping = {}
        self.result = False
        
        self.dialog = tk.Toplevel(parent) if parent else tk.Tk()
        self.setup_dialog()
    
    def setup_dialog(self):
        """إعداد نافذة الحوار"""
        self.dialog.title("تطابق الأعمدة")
        self.dialog.geometry("600x400")
        self.dialog.resizable(True, True)
        
        # إطار التعليمات
        instructions = tk.Label(
            self.dialog,
            text="يرجى تحديد تطابق الأعمدة بين الملف والنظام:",
            font=('Arial', 12, 'bold')
        )
        instructions.pack(pady=10)
        
        # إطار التطابق
        mapping_frame = tk.Frame(self.dialog)
        mapping_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # رؤوس الأعمدة
        tk.Label(mapping_frame, text="عمود النظام", font=('Arial', 10, 'bold')).grid(row=0, column=0, padx=5, pady=5)
        tk.Label(mapping_frame, text="عمود الملف", font=('Arial', 10, 'bold')).grid(row=0, column=1, padx=5, pady=5)
        
        self.mapping_combos = {}
        
        # إنشاء صفوف التطابق
        for i, (system_col, possible_names) in enumerate(self.required_columns.items(), 1):
            # تسمية العمود في النظام
            system_label = tk.Label(mapping_frame, text=possible_names[0])
            system_label.grid(row=i, column=0, padx=5, pady=2, sticky='w')
            
            # قائمة منسدلة لاختيار العمود من الملف
            combo = tk.StringVar()
            combo_widget = tk.OptionMenu(mapping_frame, combo, "-- لا يوجد --", *self.file_columns)
            combo_widget.grid(row=i, column=1, padx=5, pady=2, sticky='ew')
            
            # تعيين القيمة المقترحة
            if system_col in self.suggested_mapping:
                combo.set(self.suggested_mapping[system_col])
            
            self.mapping_combos[system_col] = combo
        
        # ضبط عرض الأعمدة
        mapping_frame.grid_columnconfigure(1, weight=1)
        
        # أزرار التحكم
        buttons_frame = tk.Frame(self.dialog)
        buttons_frame.pack(pady=20)
        
        tk.Button(
            buttons_frame,
            text="موافق",
            command=self.accept,
            bg='#4CAF50',
            fg='white',
            font=('Arial', 10, 'bold'),
            padx=20
        ).pack(side='left', padx=10)
        
        tk.Button(
            buttons_frame,
            text="إلغاء",
            command=self.cancel,
            bg='#f44336',
            fg='white',
            font=('Arial', 10, 'bold'),
            padx=20
        ).pack(side='left', padx=10)
    
    def accept(self):
        """قبول التطابق"""
        for system_col, combo_var in self.mapping_combos.items():
            file_col = combo_var.get()
            if file_col != "-- لا يوجد --":
                self.mapping[system_col] = file_col
        
        self.result = True
        self.dialog.destroy()
    
    def cancel(self):
        """إلغاء التطابق"""
        self.result = False
        self.dialog.destroy()
    
    def show(self) -> bool:
        """عرض النافذة وانتظار النتيجة"""
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        self.dialog.wait_window()
        return self.result
    
    def get_mapping(self) -> Dict[str, str]:
        """الحصول على التطابق المحدد"""
        return self.mapping
