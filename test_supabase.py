# -*- coding: utf-8 -*-
"""
اختبار الاتصال بـ Supabase
Test Supabase Connection
"""

import sys
import os

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from supabase import create_client, Client
    from config import SUPABASE_CONFIG
    print("✅ تم استيراد مكتبة Supabase بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد مكتبة Supabase: {e}")
    print("يرجى تثبيت المكتبة باستخدام: pip install supabase")
    sys.exit(1)

def test_connection():
    """اختبار الاتصال بـ Supabase"""
    try:
        print("🔗 محاولة الاتصال بـ Supabase...")
        print(f"URL: {SUPABASE_CONFIG['url']}")
        
        # إنشاء العميل
        supabase: Client = create_client(
            SUPABASE_CONFIG['url'],
            SUPABASE_CONFIG['anon_key']
        )
        
        print("✅ تم إنشاء العميل بنجاح")
        
        # اختبار الاتصال بجلب معلومات المشروع
        try:
            # محاولة جلب بيانات من جدول غير موجود للتأكد من الاتصال
            result = supabase.table('_test_connection').select('*').limit(1).execute()
            print("✅ الاتصال يعمل بشكل صحيح")
            return True
        except Exception as e:
            if "relation" in str(e).lower() or "table" in str(e).lower():
                print("✅ الاتصال يعمل بشكل صحيح (الجدول غير موجود كما هو متوقع)")
                return True
            else:
                print(f"❌ خطأ في الاتصال: {e}")
                return False
                
    except Exception as e:
        print(f"❌ فشل الاتصال بـ Supabase: {e}")
        return False

def test_service_key():
    """اختبار مفتاح الخدمة"""
    try:
        print("\n🔑 اختبار مفتاح الخدمة...")
        
        service_client: Client = create_client(
            SUPABASE_CONFIG['url'],
            SUPABASE_CONFIG['service_key']
        )
        
        print("✅ تم إنشاء عميل الخدمة بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في مفتاح الخدمة: {e}")
        return False

def create_test_table():
    """إنشاء جدول تجريبي"""
    try:
        print("\n📋 محاولة إنشاء جدول تجريبي...")
        
        # استخدام مفتاح الخدمة للعمليات الإدارية
        service_client: Client = create_client(
            SUPABASE_CONFIG['url'],
            SUPABASE_CONFIG['service_key']
        )
        
        # محاولة إنشاء جدول تجريبي بسيط
        sql_query = """
        CREATE TABLE IF NOT EXISTS test_table (
            id SERIAL PRIMARY KEY,
            name VARCHAR(100),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        """
        
        # ملاحظة: هذا يتطلب تفعيل RPC في Supabase
        print("⚠️ لإنشاء الجداول، يرجى استخدام SQL Editor في لوحة تحكم Supabase")
        print("أو تشغيل: python setup_database.py")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجدول التجريبي: {e}")
        return False

def test_basic_operations():
    """اختبار العمليات الأساسية"""
    try:
        print("\n🧪 اختبار العمليات الأساسية...")
        
        supabase: Client = create_client(
            SUPABASE_CONFIG['url'],
            SUPABASE_CONFIG['anon_key']
        )
        
        # محاولة جلب قائمة الجداول (قد لا تعمل مع anon key)
        try:
            # هذا مجرد اختبار للاتصال
            result = supabase.table('students').select('id').limit(1).execute()
            print("✅ جدول الطلاب موجود ويمكن الوصول إليه")
            print(f"البيانات: {result.data}")
        except Exception as e:
            if "relation" in str(e).lower():
                print("⚠️ جدول الطلاب غير موجود بعد - يحتاج إلى إنشاء")
            else:
                print(f"❌ خطأ في الوصول لجدول الطلاب: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في العمليات الأساسية: {e}")
        return False

def print_config_info():
    """طباعة معلومات الإعداد"""
    print("\n📋 معلومات الإعداد:")
    print(f"URL: {SUPABASE_CONFIG['url']}")
    print(f"Anon Key: {SUPABASE_CONFIG['anon_key'][:20]}...")
    print(f"Service Key: {SUPABASE_CONFIG['service_key'][:20]}...")

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار الاتصال بـ Supabase")
    print("=" * 40)
    
    print_config_info()
    
    # اختبار الاتصال الأساسي
    if test_connection():
        print("\n✅ اختبار الاتصال الأساسي نجح")
    else:
        print("\n❌ فشل اختبار الاتصال الأساسي")
        return
    
    # اختبار مفتاح الخدمة
    if test_service_key():
        print("✅ اختبار مفتاح الخدمة نجح")
    else:
        print("❌ فشل اختبار مفتاح الخدمة")
    
    # اختبار العمليات الأساسية
    test_basic_operations()
    
    # اختبار إنشاء جدول
    create_test_table()
    
    print("\n" + "=" * 40)
    print("🎉 انتهى الاختبار!")
    print("\nالخطوات التالية:")
    print("1. إذا كان الاتصال يعمل، شغل: python setup_database.py")
    print("2. أو انسخ أوامر SQL من setup_database.py إلى Supabase SQL Editor")
    print("3. ثم شغل التطبيق: python simple_app.py")

if __name__ == "__main__":
    main()
