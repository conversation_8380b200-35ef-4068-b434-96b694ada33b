# -*- coding: utf-8 -*-
"""
مكونات الواجهة الرئيسية
School Financial Management System - UI Components
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
from typing import Dict, List, Callable, Any
from datetime import datetime
import threading
from config import COLORS, FONTS, EMOJIS, get_color, get_font, get_emoji

# إعداد CustomTkinter
ctk.set_appearance_mode("light")
ctk.set_default_color_theme("blue")

class ModernFrame(ctk.CTkFrame):
    """إطار حديث مخصص"""
    
    def __init__(self, parent, title: str = "", **kwargs):
        super().__init__(parent, **kwargs)
        self.title = title
        self.setup_frame()
    
    def setup_frame(self):
        """إعداد الإطار"""
        if self.title:
            title_label = ctk.CTkLabel(
                self, 
                text=self.title,
                font=get_font('subtitle'),
                text_color=get_color('primary')
            )
            title_label.pack(pady=(10, 5), padx=10, anchor='w')

class ModernButton(ctk.CTkButton):
    """زر حديث مخصص"""
    
    def __init__(self, parent, text: str, emoji: str = "", command=None, **kwargs):
        display_text = f"{emoji} {text}" if emoji else text
        
        super().__init__(
            parent,
            text=display_text,
            command=command,
            font=get_font('normal'),
            corner_radius=8,
            **kwargs
        )

class ModernEntry(ctk.CTkEntry):
    """حقل إدخال حديث"""
    
    def __init__(self, parent, placeholder: str = "", **kwargs):
        super().__init__(
            parent,
            placeholder_text=placeholder,
            font=get_font('normal'),
            corner_radius=8,
            **kwargs
        )

class StatusBar(tk.Frame):
    """شريط الحالة"""
    
    def __init__(self, parent):
        super().__init__(parent, bg=get_color('light'), height=30)
        self.pack_propagate(False)
        
        # رسالة الحالة
        self.status_label = tk.Label(
            self,
            text="🟢 جاهز",
            bg=get_color('light'),
            fg=get_color('dark'),
            font=get_font('small'),
            anchor='w'
        )
        self.status_label.pack(side='left', padx=10, pady=5)
        
        # معلومات إضافية
        self.info_label = tk.Label(
            self,
            text="",
            bg=get_color('light'),
            fg=get_color('dark'),
            font=get_font('small'),
            anchor='e'
        )
        self.info_label.pack(side='right', padx=10, pady=5)
    
    def set_status(self, message: str, status_type: str = "info"):
        """تحديث رسالة الحالة"""
        emoji_map = {
            'success': '🟢',
            'warning': '🟡',
            'error': '🔴',
            'info': '🔵',
            'loading': '⏳'
        }
        
        emoji = emoji_map.get(status_type, '🔵')
        self.status_label.config(text=f"{emoji} {message}")
        
        # تحديث اللون حسب النوع
        color_map = {
            'success': get_color('success'),
            'warning': get_color('warning'),
            'error': get_color('warning'),
            'info': get_color('info'),
            'loading': get_color('primary')
        }
        
        self.status_label.config(fg=color_map.get(status_type, get_color('dark')))
    
    def set_info(self, info: str):
        """تحديث المعلومات الإضافية"""
        self.info_label.config(text=info)

class ProgressDialog(tk.Toplevel):
    """نافذة شريط التقدم"""
    
    def __init__(self, parent, title: str, message: str):
        super().__init__(parent)
        self.title(title)
        self.geometry("400x150")
        self.resizable(False, False)
        self.transient(parent)
        self.grab_set()
        
        # توسيط النافذة
        self.center_window()
        
        # الرسالة
        message_label = tk.Label(
            self,
            text=message,
            font=get_font('normal'),
            wraplength=350
        )
        message_label.pack(pady=20)
        
        # شريط التقدم
        self.progress = ttk.Progressbar(
            self,
            mode='indeterminate',
            length=300
        )
        self.progress.pack(pady=10)
        self.progress.start()
        
        # زر الإلغاء
        cancel_btn = ModernButton(
            self,
            text="إلغاء",
            emoji="❌",
            command=self.destroy
        )
        cancel_btn.pack(pady=10)
    
    def center_window(self):
        """توسيط النافذة"""
        self.update_idletasks()
        x = (self.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.winfo_screenheight() // 2) - (150 // 2)
        self.geometry(f"400x150+{x}+{y}")

class SearchFrame(ModernFrame):
    """إطار البحث المتقدم"""
    
    def __init__(self, parent, search_callback: Callable):
        super().__init__(parent, title="🔍 البحث المتقدم")
        self.search_callback = search_callback
        self.setup_search_frame()
    
    def setup_search_frame(self):
        """إعداد إطار البحث"""
        # الصف الأول - البحث العام
        search_row = tk.Frame(self)
        search_row.pack(fill='x', padx=10, pady=5)
        
        tk.Label(search_row, text="البحث:", font=get_font('normal')).pack(side='left')
        
        self.search_entry = ModernEntry(
            search_row,
            placeholder="ابحث عن طالب...",
            width=200
        )
        self.search_entry.pack(side='left', padx=(10, 5))
        self.search_entry.bind('<KeyRelease>', self.on_search_change)
        
        search_btn = ModernButton(
            search_row,
            text="بحث",
            emoji="🔍",
            command=self.perform_search,
            width=80
        )
        search_btn.pack(side='left', padx=5)
        
        # الصف الثاني - الفلاتر
        filter_row = tk.Frame(self)
        filter_row.pack(fill='x', padx=10, pady=5)
        
        # فلتر المرحلة
        tk.Label(filter_row, text="المرحلة:", font=get_font('normal')).pack(side='left')
        self.stage_combo = ttk.Combobox(
            filter_row,
            values=['الكل', 'ابتدائي', 'إعدادي', 'ثانوي'],
            state='readonly',
            width=10
        )
        self.stage_combo.set('الكل')
        self.stage_combo.pack(side='left', padx=(5, 10))
        self.stage_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # فلتر الفصل
        tk.Label(filter_row, text="الفصل:", font=get_font('normal')).pack(side='left')
        self.class_combo = ttk.Combobox(
            filter_row,
            values=['الكل', 'الأول', 'الثاني', 'الثالث', 'الرابع', 'الخامس', 'السادس'],
            state='readonly',
            width=10
        )
        self.class_combo.set('الكل')
        self.class_combo.pack(side='left', padx=(5, 10))
        self.class_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # زر مسح الفلاتر
        clear_btn = ModernButton(
            filter_row,
            text="مسح",
            emoji="🗑️",
            command=self.clear_filters,
            width=80
        )
        clear_btn.pack(side='right', padx=5)
    
    def on_search_change(self, event=None):
        """عند تغيير نص البحث"""
        # البحث التلقائي بعد توقف الكتابة
        if hasattr(self, 'search_timer'):
            self.after_cancel(self.search_timer)
        self.search_timer = self.after(500, self.perform_search)
    
    def on_filter_change(self, event=None):
        """عند تغيير الفلاتر"""
        self.perform_search()
    
    def perform_search(self):
        """تنفيذ البحث"""
        search_term = self.search_entry.get()
        filters = {
            'stage': self.stage_combo.get() if self.stage_combo.get() != 'الكل' else None,
            'class': self.class_combo.get() if self.class_combo.get() != 'الكل' else None
        }
        
        if self.search_callback:
            self.search_callback(search_term, filters)
    
    def clear_filters(self):
        """مسح جميع الفلاتر"""
        self.search_entry.delete(0, 'end')
        self.stage_combo.set('الكل')
        self.class_combo.set('الكل')
        self.perform_search()

class NotificationManager:
    """مدير الإشعارات"""
    
    def __init__(self, parent):
        self.parent = parent
        self.notifications = []
    
    def show_notification(self, message: str, notification_type: str = "info", duration: int = 3000):
        """عرض إشعار"""
        notification = NotificationWidget(self.parent, message, notification_type)
        self.notifications.append(notification)
        
        # إزالة الإشعار بعد المدة المحددة
        self.parent.after(duration, lambda: self.remove_notification(notification))
    
    def remove_notification(self, notification):
        """إزالة إشعار"""
        if notification in self.notifications:
            notification.destroy()
            self.notifications.remove(notification)
    
    def clear_all(self):
        """مسح جميع الإشعارات"""
        for notification in self.notifications:
            notification.destroy()
        self.notifications.clear()

class NotificationWidget(tk.Toplevel):
    """ويدجت الإشعار"""
    
    def __init__(self, parent, message: str, notification_type: str):
        super().__init__(parent)
        
        # إعداد النافذة
        self.overrideredirect(True)  # إزالة شريط العنوان
        self.attributes('-topmost', True)  # دائماً في المقدمة
        
        # ألوان الإشعار
        colors = {
            'success': {'bg': get_color('success'), 'fg': 'white'},
            'warning': {'bg': get_color('warning'), 'fg': 'white'},
            'error': {'bg': get_color('warning'), 'fg': 'white'},
            'info': {'bg': get_color('info'), 'fg': 'white'}
        }
        
        color_scheme = colors.get(notification_type, colors['info'])
        
        # الرموز التعبيرية
        emojis = {
            'success': '✅',
            'warning': '⚠️',
            'error': '❌',
            'info': 'ℹ️'
        }
        
        emoji = emojis.get(notification_type, 'ℹ️')
        
        # إنشاء الإطار
        frame = tk.Frame(
            self,
            bg=color_scheme['bg'],
            relief='raised',
            bd=2
        )
        frame.pack(fill='both', expand=True)
        
        # النص
        label = tk.Label(
            frame,
            text=f"{emoji} {message}",
            bg=color_scheme['bg'],
            fg=color_scheme['fg'],
            font=get_font('normal'),
            padx=20,
            pady=10
        )
        label.pack()
        
        # تحديد الموقع
        self.position_notification()
        
        # تأثير الظهور
        self.animate_in()
    
    def position_notification(self):
        """تحديد موقع الإشعار"""
        self.update_idletasks()
        
        # الحصول على أبعاد الشاشة
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        
        # أبعاد الإشعار
        width = self.winfo_reqwidth()
        height = self.winfo_reqheight()
        
        # موقع الإشعار (أعلى يمين الشاشة)
        x = screen_width - width - 20
        y = 20
        
        self.geometry(f"{width}x{height}+{x}+{y}")
    
    def animate_in(self):
        """تأثير الظهور"""
        self.attributes('-alpha', 0.0)
        self.fade_in()
    
    def fade_in(self, alpha=0.0):
        """تأثير التلاشي للداخل"""
        alpha += 0.1
        if alpha <= 1.0:
            self.attributes('-alpha', alpha)
            self.after(50, lambda: self.fade_in(alpha))

class DataTable(tk.Frame):
    """جدول البيانات المتقدم"""
    
    def __init__(self, parent, columns: List[Dict], **kwargs):
        super().__init__(parent, **kwargs)
        self.columns = columns
        self.data = []
        self.selected_row = None
        self.setup_table()
    
    def setup_table(self):
        """إعداد الجدول"""
        # إطار الجدول مع شريط التمرير
        table_frame = tk.Frame(self)
        table_frame.pack(fill='both', expand=True)
        
        # إنشاء Treeview
        self.tree = ttk.Treeview(table_frame, show='headings', selectmode='extended')
        
        # تحديد الأعمدة
        column_ids = [col['id'] for col in self.columns]
        self.tree['columns'] = column_ids
        
        # إعداد الأعمدة
        for col in self.columns:
            self.tree.heading(col['id'], text=col['text'], anchor='center')
            self.tree.column(
                col['id'],
                width=col.get('width', 100),
                minwidth=col.get('minwidth', 50),
                anchor=col.get('anchor', 'center')
            )
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.tree.xview)
        self.tree.configure(xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # إعداد الشبكة
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # ربط الأحداث
        self.tree.bind('<<TreeviewSelect>>', self.on_select)
        self.tree.bind('<Double-1>', self.on_double_click)
    
    def load_data(self, data: List[Dict]):
        """تحميل البيانات في الجدول"""
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        self.data = data
        
        # إضافة البيانات الجديدة
        for i, row in enumerate(data):
            values = []
            for col in self.columns:
                value = row.get(col['id'], '')
                # تنسيق القيم حسب النوع
                if col.get('type') == 'currency':
                    value = f"{float(value):,.2f}" if value else "0.00"
                elif col.get('type') == 'date':
                    if value:
                        try:
                            date_obj = datetime.strptime(str(value), '%Y-%m-%d')
                            value = date_obj.strftime('%d/%m/%Y')
                        except:
                            pass
                values.append(str(value))
            
            # تحديد لون الصف
            tags = []
            if i % 2 == 0:
                tags.append('evenrow')
            else:
                tags.append('oddrow')
            
            self.tree.insert('', 'end', values=values, tags=tags)
        
        # تطبيق الألوان
        self.tree.tag_configure('evenrow', background=get_color('table_row1'))
        self.tree.tag_configure('oddrow', background=get_color('table_row2'))
    
    def on_select(self, event):
        """عند تحديد صف"""
        selection = self.tree.selection()
        if selection:
            item = selection[0]
            self.selected_row = self.tree.index(item)
    
    def on_double_click(self, event):
        """عند النقر المزدوج"""
        if self.selected_row is not None:
            # يمكن إضافة منطق فتح نافذة التحرير هنا
            pass
    
    def get_selected_data(self):
        """الحصول على البيانات المحددة"""
        if self.selected_row is not None and self.selected_row < len(self.data):
            return self.data[self.selected_row]
        return None
    
    def refresh(self):
        """تحديث الجدول"""
        self.load_data(self.data)
