# -*- coding: utf-8 -*-
"""
مشغل التطبيق - نظام إدارة البيانات المالية للمدرسة
Application Launcher - School Financial Management System
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import sys
import os

class AppLauncher:
    """مشغل التطبيق"""
    
    def __init__(self):
        """تهيئة المشغل"""
        self.root = tk.Tk()
        self.setup_window()
        self.setup_ui()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.root.title("🏫 مشغل نظام إدارة البيانات المالية للمدرسة")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        # توسيط النافذة
        self.center_window()
        
        # ألوان
        self.colors = {
            'primary': '#2E86AB',
            'success': '#F18F01',
            'warning': '#C73E1D',
            'info': '#4ECDC4',
            'light': '#F8F9FA',
            'dark': '#343A40'
        }
        
        self.root.configure(bg=self.colors['light'])
    
    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (300)
        y = (self.root.winfo_screenheight() // 2) - (250)
        self.root.geometry(f"600x500+{x}+{y}")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg=self.colors['primary'], height=80)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame,
            text="🏫 نظام إدارة البيانات المالية للمدرسة",
            font=('Arial', 18, 'bold'),
            bg=self.colors['primary'],
            fg='white'
        )
        title_label.pack(expand=True)
        
        # الوصف
        desc_frame = tk.Frame(self.root, bg=self.colors['light'])
        desc_frame.pack(fill='x', padx=20, pady=20)
        
        desc_text = """
نظام شامل ومتطور لإدارة البيانات المالية للطلاب في المدارس
مصمم خصيصاً للمدارس العربية مع واجهة مستخدم حديثة وميزات متقدمة

✨ الميزات الرئيسية:
• واجهة مستخدم عصرية وسهلة الاستخدام
• إدارة شاملة للرسوم والمدفوعات
• نظام أقساط متطور مع 9 أقساط
• خصومات ذكية (الأخوة، المدرسين، الدفع المقدم)
• استيراد وتصدير البيانات من/إلى Excel
• نقل البيانات بين السنوات الدراسية
• تقارير مالية شاملة وإحصائيات فورية
        """
        
        desc_label = tk.Label(
            desc_frame,
            text=desc_text.strip(),
            font=('Arial', 10),
            bg=self.colors['light'],
            fg=self.colors['dark'],
            justify='right',
            wraplength=550
        )
        desc_label.pack()
        
        # أزرار التشغيل
        buttons_frame = tk.Frame(self.root, bg=self.colors['light'])
        buttons_frame.pack(fill='x', padx=40, pady=20)
        
        # زر التشغيل السريع
        quick_btn = tk.Button(
            buttons_frame,
            text="🚀 تشغيل سريع (النسخة المبسطة)",
            command=self.run_simple_app,
            font=('Arial', 12, 'bold'),
            bg=self.colors['success'],
            fg='white',
            relief='flat',
            pady=15,
            cursor='hand2'
        )
        quick_btn.pack(fill='x', pady=5)
        
        # زر التشغيل الكامل
        full_btn = tk.Button(
            buttons_frame,
            text="⚡ تشغيل كامل (جميع الميزات)",
            command=self.run_full_app,
            font=('Arial', 12, 'bold'),
            bg=self.colors['primary'],
            fg='white',
            relief='flat',
            pady=15,
            cursor='hand2'
        )
        full_btn.pack(fill='x', pady=5)
        
        # زر تثبيت المتطلبات
        install_btn = tk.Button(
            buttons_frame,
            text="📦 تثبيت المتطلبات",
            command=self.install_requirements,
            font=('Arial', 11),
            bg=self.colors['info'],
            fg='white',
            relief='flat',
            pady=10,
            cursor='hand2'
        )
        install_btn.pack(fill='x', pady=5)
        
        # زر اختبار النظام
        test_btn = tk.Button(
            buttons_frame,
            text="🧪 اختبار النظام",
            command=self.test_system,
            font=('Arial', 11),
            bg=self.colors['warning'],
            fg='white',
            relief='flat',
            pady=10,
            cursor='hand2'
        )
        test_btn.pack(fill='x', pady=5)
        
        # معلومات إضافية
        info_frame = tk.Frame(self.root, bg=self.colors['light'])
        info_frame.pack(fill='x', padx=20, pady=10)
        
        info_text = """
💡 نصائح:
• للتشغيل السريع: لا يحتاج مكتبات إضافية
• للتشغيل الكامل: يحتاج تثبيت المتطلبات أولاً
• لأول مرة: ننصح بالتشغيل السريع للتجربة
        """
        
        info_label = tk.Label(
            info_frame,
            text=info_text.strip(),
            font=('Arial', 9),
            bg=self.colors['light'],
            fg='gray',
            justify='right'
        )
        info_label.pack()
        
        # زر الخروج
        exit_btn = tk.Button(
            self.root,
            text="❌ خروج",
            command=self.root.quit,
            font=('Arial', 10),
            bg='lightgray',
            relief='flat',
            pady=5
        )
        exit_btn.pack(pady=10)
    
    def run_simple_app(self):
        """تشغيل التطبيق المبسط"""
        try:
            self.show_loading("جاري تشغيل النسخة المبسطة...")
            
            # إخفاء النافذة الحالية
            self.root.withdraw()
            
            # تشغيل التطبيق المبسط
            result = subprocess.run([sys.executable, 'simple_app.py'], 
                                  capture_output=False, 
                                  text=True)
            
            # إظهار النافذة مرة أخرى
            self.root.deiconify()
            
            if result.returncode == 0:
                messagebox.showinfo("نجح", "✅ تم إغلاق التطبيق بنجاح")
            else:
                messagebox.showerror("خطأ", f"❌ خطأ في تشغيل التطبيق")
                
        except FileNotFoundError:
            messagebox.showerror("خطأ", "❌ ملف simple_app.py غير موجود")
        except Exception as e:
            messagebox.showerror("خطأ", f"❌ خطأ في التشغيل: {e}")
    
    def run_full_app(self):
        """تشغيل التطبيق الكامل"""
        try:
            # التحقق من وجود المتطلبات
            if not self.check_requirements():
                if messagebox.askyesno("متطلبات مفقودة", 
                                     "❌ بعض المتطلبات غير مثبتة.\n\nهل تريد تثبيتها الآن؟"):
                    self.install_requirements()
                    return
                else:
                    return
            
            self.show_loading("جاري تشغيل النسخة الكاملة...")
            
            # إخفاء النافذة الحالية
            self.root.withdraw()
            
            # تشغيل التطبيق الكامل
            result = subprocess.run([sys.executable, 'main.py'], 
                                  capture_output=False, 
                                  text=True)
            
            # إظهار النافذة مرة أخرى
            self.root.deiconify()
            
            if result.returncode == 0:
                messagebox.showinfo("نجح", "✅ تم إغلاق التطبيق بنجاح")
            else:
                messagebox.showerror("خطأ", f"❌ خطأ في تشغيل التطبيق")
                
        except FileNotFoundError:
            messagebox.showerror("خطأ", "❌ ملف main.py غير موجود")
        except Exception as e:
            messagebox.showerror("خطأ", f"❌ خطأ في التشغيل: {e}")
    
    def install_requirements(self):
        """تثبيت المتطلبات"""
        try:
            self.show_loading("جاري تثبيت المتطلبات...")
            
            # تشغيل pip install
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                                  capture_output=True, 
                                  text=True)
            
            if result.returncode == 0:
                messagebox.showinfo("نجح", "✅ تم تثبيت المتطلبات بنجاح!")
            else:
                messagebox.showerror("خطأ", f"❌ خطأ في التثبيت:\n{result.stderr}")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"❌ خطأ في تثبيت المتطلبات: {e}")
    
    def test_system(self):
        """اختبار النظام"""
        try:
            self.show_loading("جاري اختبار النظام...")
            
            # تشغيل اختبار النظام
            result = subprocess.run([sys.executable, 'simple_test.py'], 
                                  capture_output=False, 
                                  text=True)
            
            if result.returncode == 0:
                messagebox.showinfo("اختبار", "✅ تم اختبار النظام")
            else:
                messagebox.showerror("خطأ", "❌ فشل في اختبار النظام")
                
        except FileNotFoundError:
            messagebox.showerror("خطأ", "❌ ملف simple_test.py غير موجود")
        except Exception as e:
            messagebox.showerror("خطأ", f"❌ خطأ في الاختبار: {e}")
    
    def check_requirements(self):
        """فحص المتطلبات"""
        try:
            import supabase
            import pandas
            return True
        except ImportError:
            return False
    
    def show_loading(self, message):
        """عرض رسالة تحميل"""
        # يمكن تحسين هذا بإضافة نافذة تحميل
        print(f"⏳ {message}")
    
    def run(self):
        """تشغيل المشغل"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        launcher = AppLauncher()
        launcher.run()
    except Exception as e:
        print(f"❌ خطأ في تشغيل المشغل: {e}")

if __name__ == "__main__":
    main()
