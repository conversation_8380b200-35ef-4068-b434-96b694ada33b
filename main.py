# -*- coding: utf-8 -*-
"""
النظام الرئيسي لإدارة البيانات المالية للمدرسة
School Financial Management System - Main Application
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
import threading
import asyncio
from datetime import datetime
import sys
import os

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import *
from database import DatabaseManager
from ui_components import *
from data_manager import DataManager
from year_transfer import YearTransferManager

class SchoolFinanceApp:
    """التطبيق الرئيسي لإدارة البيانات المالية للمدرسة"""
    
    def __init__(self):
        """تهيئة التطبيق"""
        self.setup_window()
        self.setup_database()
        self.setup_managers()
        self.setup_ui()
        self.setup_menu()
        self.load_initial_data()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root = ctk.CTk()
        self.root.title(APP_CONFIG['window_title'])
        self.root.geometry(APP_CONFIG['window_size'])
        self.root.minsize(*APP_CONFIG['min_window_size'])
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد الشبكة الرئيسية
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(1, weight=1)
        
        # متغيرات التطبيق
        self.current_academic_year = tk.StringVar(value=SCHOOL_SETTINGS['current_year'])
        self.students_data = []
        self.filtered_data = []
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            self.db_manager = DatabaseManager()
            # اختبار الاتصال
            if self.db_manager.test_connection():
                print("✅ تم الاتصال بقاعدة البيانات بنجاح")
            else:
                messagebox.showerror("خطأ", "فشل الاتصال بقاعدة البيانات")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إعداد قاعدة البيانات: {e}")
            self.db_manager = None
    
    def setup_managers(self):
        """إعداد المديرين المساعدين"""
        self.data_manager = DataManager(self.db_manager)
        self.year_transfer_manager = YearTransferManager(self.db_manager)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الشريط الجانبي
        self.setup_sidebar()
        
        # المنطقة الرئيسية
        self.setup_main_area()
        
        # شريط الحالة
        self.setup_status_bar()
        
        # مدير الإشعارات
        self.notification_manager = NotificationManager(self.root)
    
    def setup_sidebar(self):
        """إعداد الشريط الجانبي"""
        self.sidebar = ctk.CTkFrame(self.root, width=250, corner_radius=0)
        self.sidebar.grid(row=0, column=0, sticky="nsew", padx=0, pady=0)
        self.sidebar.grid_propagate(False)
        
        # عنوان التطبيق
        title_label = ctk.CTkLabel(
            self.sidebar,
            text="🏫 إدارة البيانات المالية",
            font=get_font('title'),
            text_color="white"
        )
        title_label.pack(pady=20, padx=20)
        
        # معلومات السنة الدراسية
        year_frame = ctk.CTkFrame(self.sidebar)
        year_frame.pack(fill='x', padx=20, pady=10)
        
        ctk.CTkLabel(
            year_frame,
            text="📅 السنة الدراسية:",
            font=get_font('normal')
        ).pack(pady=5)
        
        self.year_combo = ctk.CTkComboBox(
            year_frame,
            values=SCHOOL_SETTINGS['academic_years'],
            variable=self.current_academic_year,
            command=self.on_year_change
        )
        self.year_combo.pack(pady=5, padx=10, fill='x')
        
        # الإحصائيات السريعة
        self.stats_frame = ctk.CTkFrame(self.sidebar)
        self.stats_frame.pack(fill='x', padx=20, pady=10)
        
        ctk.CTkLabel(
            self.stats_frame,
            text="📊 إحصائيات سريعة",
            font=get_font('subtitle')
        ).pack(pady=10)
        
        # إحصائيات ديناميكية
        self.stats_labels = {}
        stats_items = [
            ('total_students', '👨‍🎓 إجمالي الطلاب', '0'),
            ('total_fees', '💰 إجمالي الرسوم', '0.00'),
            ('total_paid', '✅ إجمالي المدفوع', '0.00'),
            ('total_remaining', '⏳ المتبقي', '0.00'),
            ('collection_rate', '📈 نسبة التحصيل', '0%')
        ]
        
        for key, label, default in stats_items:
            stat_frame = tk.Frame(self.stats_frame, bg='transparent')
            stat_frame.pack(fill='x', padx=10, pady=2)
            
            tk.Label(
                stat_frame,
                text=label,
                font=get_font('small'),
                bg='transparent',
                fg='white'
            ).pack(side='left')
            
            self.stats_labels[key] = tk.Label(
                stat_frame,
                text=default,
                font=get_font('small'),
                bg='transparent',
                fg='white'
            )
            self.stats_labels[key].pack(side='right')
        
        # أزرار العمليات الرئيسية
        self.setup_action_buttons()
    
    def setup_action_buttons(self):
        """إعداد أزرار العمليات"""
        buttons_frame = ctk.CTkFrame(self.sidebar)
        buttons_frame.pack(fill='x', padx=20, pady=20)
        
        ctk.CTkLabel(
            buttons_frame,
            text="⚡ العمليات السريعة",
            font=get_font('subtitle')
        ).pack(pady=10)
        
        # أزرار العمليات
        buttons = [
            ("➕ إضافة طالب", self.add_student),
            ("📥 استيراد بيانات", self.import_data),
            ("📤 تصدير بيانات", self.export_data),
            ("🔄 نقل سنة دراسية", self.transfer_year),
            ("📊 التقارير", self.show_reports),
            ("⚙️ الإعدادات", self.show_settings),
        ]
        
        for text, command in buttons:
            btn = ctk.CTkButton(
                buttons_frame,
                text=text,
                command=command,
                font=get_font('normal'),
                height=35
            )
            btn.pack(fill='x', padx=10, pady=5)
    
    def setup_main_area(self):
        """إعداد المنطقة الرئيسية"""
        self.main_frame = ctk.CTkFrame(self.root, corner_radius=0)
        self.main_frame.grid(row=0, column=1, sticky="nsew", padx=0, pady=0)
        self.main_frame.grid_rowconfigure(1, weight=1)
        self.main_frame.grid_columnconfigure(0, weight=1)
        
        # إطار البحث
        self.search_frame = SearchFrame(self.main_frame, self.perform_search)
        self.search_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        
        # جدول البيانات
        self.setup_data_table()
    
    def setup_data_table(self):
        """إعداد جدول البيانات"""
        # تحديد أعمدة الجدول
        columns = [
            {'id': 'serial_number', 'text': 'الرقم التسلسلي', 'width': 100},
            {'id': 'name', 'text': 'اسم الطالب', 'width': 200, 'anchor': 'w'},
            {'id': 'class', 'text': 'الفصل', 'width': 100},
            {'id': 'stage', 'text': 'المرحلة', 'width': 100},
            {'id': 'total_fees', 'text': 'إجمالي الرسوم', 'width': 120, 'type': 'currency'},
            {'id': 'total_paid', 'text': 'المدفوع', 'width': 120, 'type': 'currency'},
            {'id': 'remaining_amount', 'text': 'المتبقي', 'width': 120, 'type': 'currency'},
            {'id': 'total_discounts', 'text': 'الخصومات', 'width': 100, 'type': 'currency'},
            {'id': 'notes', 'text': 'ملاحظات', 'width': 150, 'anchor': 'w'}
        ]
        
        self.data_table = DataTable(self.main_frame, columns)
        self.data_table.grid(row=1, column=0, sticky="nsew", padx=10, pady=(0, 10))
    
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = StatusBar(self.root)
        self.status_bar.grid(row=1, column=0, columnspan=2, sticky="ew")
    
    def setup_menu(self):
        """إعداد شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="📥 استيراد بيانات", command=self.import_data)
        file_menu.add_command(label="📤 تصدير بيانات", command=self.export_data)
        file_menu.add_separator()
        file_menu.add_command(label="🚪 خروج", command=self.root.quit)
        
        # قائمة البيانات
        data_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="البيانات", menu=data_menu)
        data_menu.add_command(label="➕ إضافة طالب", command=self.add_student)
        data_menu.add_command(label="✏️ تعديل طالب", command=self.edit_student)
        data_menu.add_command(label="🗑️ حذف طالب", command=self.delete_student)
        data_menu.add_separator()
        data_menu.add_command(label="🔄 نقل سنة دراسية", command=self.transfer_year)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="📊 تقرير مالي شامل", command=self.show_financial_report)
        reports_menu.add_command(label="📈 تقرير التحصيل", command=self.show_collection_report)
        reports_menu.add_command(label="🏷️ تقرير الخصومات", command=self.show_discounts_report)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="❓ حول البرنامج", command=self.show_about)
    
    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        self.status_bar.set_status("جاري تحميل البيانات...", "loading")
        
        # تحميل البيانات في خيط منفصل
        threading.Thread(target=self._load_data_thread, daemon=True).start()
    
    def _load_data_thread(self):
        """تحميل البيانات في خيط منفصل"""
        try:
            if self.db_manager:
                # تحميل بيانات الطلاب
                self.students_data = self.db_manager.get_all_students(
                    self.current_academic_year.get()
                )
                
                # تحديث الواجهة في الخيط الرئيسي
                self.root.after(0, self._update_ui_after_load)
            else:
                # بيانات تجريبية في حالة عدم وجود قاعدة بيانات
                self.students_data = self._get_sample_data()
                self.root.after(0, self._update_ui_after_load)
        except Exception as e:
            self.root.after(0, lambda: self.show_error(f"خطأ في تحميل البيانات: {e}"))
    
    def _update_ui_after_load(self):
        """تحديث الواجهة بعد تحميل البيانات"""
        self.filtered_data = self.students_data.copy()
        self.data_table.load_data(self.filtered_data)
        self.update_statistics()
        self.status_bar.set_status(f"تم تحميل {len(self.students_data)} طالب", "success")
    
    def _get_sample_data(self):
        """الحصول على بيانات تجريبية"""
        return [
            {
                'id': 1,
                'serial_number': '2025001',
                'name': 'أحمد محمد علي',
                'class': 'الأول',
                'stage': 'ابتدائي',
                'total_fees': 15000.00,
                'total_paid': 10000.00,
                'remaining_amount': 5000.00,
                'total_discounts': 500.00,
                'notes': 'طالب متفوق'
            },
            {
                'id': 2,
                'serial_number': '2025002',
                'name': 'فاطمة أحمد حسن',
                'class': 'الثاني',
                'stage': 'إعدادي',
                'total_fees': 18000.00,
                'total_paid': 15000.00,
                'remaining_amount': 3000.00,
                'total_discounts': 900.00,
                'notes': 'خصم الأخوة'
            }
        ]
    
    def update_statistics(self):
        """تحديث الإحصائيات"""
        if not self.students_data:
            return
        
        total_students = len(self.students_data)
        total_fees = sum(float(s.get('total_fees', 0)) for s in self.students_data)
        total_paid = sum(float(s.get('total_paid', 0)) for s in self.students_data)
        total_remaining = total_fees - total_paid
        collection_rate = (total_paid / total_fees * 100) if total_fees > 0 else 0
        
        # تحديث التسميات
        self.stats_labels['total_students'].config(text=str(total_students))
        self.stats_labels['total_fees'].config(text=f"{total_fees:,.2f}")
        self.stats_labels['total_paid'].config(text=f"{total_paid:,.2f}")
        self.stats_labels['total_remaining'].config(text=f"{total_remaining:,.2f}")
        self.stats_labels['collection_rate'].config(text=f"{collection_rate:.1f}%")
    
    # معالجات الأحداث
    def on_year_change(self, selected_year):
        """عند تغيير السنة الدراسية"""
        self.load_initial_data()
    
    def perform_search(self, search_term: str, filters: dict):
        """تنفيذ البحث والتصفية"""
        if not self.students_data:
            return
        
        filtered = self.students_data.copy()
        
        # تطبيق البحث النصي
        if search_term:
            filtered = [
                s for s in filtered 
                if search_term.lower() in s.get('name', '').lower()
            ]
        
        # تطبيق الفلاتر
        if filters.get('stage'):
            filtered = [s for s in filtered if s.get('stage') == filters['stage']]
        
        if filters.get('class'):
            filtered = [s for s in filtered if s.get('class') == filters['class']]
        
        self.filtered_data = filtered
        self.data_table.load_data(self.filtered_data)
        
        # تحديث شريط الحالة
        self.status_bar.set_status(
            f"تم العثور على {len(filtered)} من أصل {len(self.students_data)} طالب",
            "info"
        )
    
    # العمليات الرئيسية
    def add_student(self):
        """إضافة طالب جديد"""
        self.notification_manager.show_notification("قريباً: نافذة إضافة طالب", "info")
    
    def edit_student(self):
        """تعديل طالب"""
        selected = self.data_table.get_selected_data()
        if selected:
            self.notification_manager.show_notification(f"قريباً: تعديل {selected['name']}", "info")
        else:
            self.notification_manager.show_notification("يرجى تحديد طالب للتعديل", "warning")
    
    def delete_student(self):
        """حذف طالب"""
        selected = self.data_table.get_selected_data()
        if selected:
            if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف الطالب {selected['name']}؟"):
                self.notification_manager.show_notification("تم حذف الطالب بنجاح", "success")
        else:
            self.notification_manager.show_notification("يرجى تحديد طالب للحذف", "warning")
    
    def import_data(self):
        """استيراد البيانات"""
        self.notification_manager.show_notification("قريباً: استيراد البيانات", "info")
    
    def export_data(self):
        """تصدير البيانات"""
        self.notification_manager.show_notification("قريباً: تصدير البيانات", "info")
    
    def transfer_year(self):
        """نقل السنة الدراسية"""
        self.notification_manager.show_notification("قريباً: نقل السنة الدراسية", "info")
    
    def show_reports(self):
        """عرض التقارير"""
        self.notification_manager.show_notification("قريباً: التقارير", "info")
    
    def show_settings(self):
        """عرض الإعدادات"""
        self.notification_manager.show_notification("قريباً: الإعدادات", "info")
    
    def show_financial_report(self):
        """عرض التقرير المالي"""
        self.notification_manager.show_notification("قريباً: التقرير المالي", "info")
    
    def show_collection_report(self):
        """عرض تقرير التحصيل"""
        self.notification_manager.show_notification("قريباً: تقرير التحصيل", "info")
    
    def show_discounts_report(self):
        """عرض تقرير الخصومات"""
        self.notification_manager.show_notification("قريباً: تقرير الخصومات", "info")
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = """
🏫 نظام إدارة البيانات المالية للمدرسة
الإصدار 1.0

تم تطويره بواسطة: فريق التطوير
تاريخ الإصدار: 2025

البرنامج مصمم لإدارة البيانات المالية للطلاب
مع إمكانيات متقدمة للبحث والتصفية والتقارير
        """
        messagebox.showinfo("حول البرنامج", about_text)
    
    def show_error(self, message: str):
        """عرض رسالة خطأ"""
        self.status_bar.set_status(message, "error")
        self.notification_manager.show_notification(message, "error")
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            # إنشاء المجلدات المطلوبة
            create_directories()
            
            # تشغيل التطبيق
            self.root.mainloop()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تشغيل التطبيق: {e}")

def main():
    """الدالة الرئيسية"""
    try:
        app = SchoolFinanceApp()
        app.run()
    except Exception as e:
        messagebox.showerror("خطأ", f"خطأ في بدء التطبيق: {e}")

if __name__ == "__main__":
    main()
