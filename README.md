# 🏫 نظام إدارة البيانات المالية للمدرسة

## 📋 نظرة عامة

نظام شامل ومتطور لإدارة البيانات المالية للطلاب في المدارس، مصمم خصيصاً للمدارس العربية مع واجهة مستخدم حديثة وميزات متقدمة.

## ✨ الميزات الرئيسية

### 🎨 واجهة المستخدم
- **تصميم عصري**: واجهة تشغل 90% من مساحة الشاشة
- **ألوان جذابة**: نظام ألوان متدرج وحديث
- **أيقونات تعبيرية**: رموز emoji لتحسين تجربة المستخدم
- **شريط حالة ديناميكي**: عرض الحالة والإشعارات
- **لوحة تحكم جانبية**: منظمة ومرتبة

### 📊 إدارة البيانات
- **استيراد ذكي**: من Excel/CSV مع تطابق أعمدة ذكي
- **تصدير متقدم**: إلى Excel مع تنسيق احترافي
- **بحث وتصفية**: نظام بحث متقدم متعدد المعايير
- **التحقق من البيانات**: فحص صحة البيانات تلقائياً

### 💰 النظام المالي
- **إدارة الرسوم**: هيكل رسوم مرن لكل مرحلة دراسية
- **نظام الأقساط**: تتبع 9 أقساط مع التواريخ والإيصالات
- **الخصومات الذكية**: خصم الأخوة، المدرسين، الدفع المقدم
- **المديونيات**: تتبع المديونيات السابقة والحالية

### 🔄 نقل السنوات الدراسية
- **نقل ذكي**: نقل البيانات بين السنوات مع خيارات متقدمة
- **ترقية المراحل**: ترقية تلقائية للطلاب
- **معاينة التغييرات**: عرض التغييرات قبل التنفيذ
- **خيارات مرنة**: إعادة تصفير المدفوعات، نقل المديونيات

## 🚀 التثبيت والتشغيل

### المتطلبات الأساسية
```bash
Python 3.8+
tkinter (مدمج مع Python)
```

### التشغيل السريع (بدون مكتبات إضافية)
```bash
python simple_app.py
```

### التثبيت الكامل (للميزات المتقدمة)
```bash
pip install -r requirements.txt
python main.py
```

## 📁 هيكل المشروع

```
move-finance/
├── 📄 main.py                 # التطبيق الرئيسي الكامل
├── 📄 simple_app.py          # النسخة المبسطة
├── 📄 config.py              # إعدادات التطبيق
├── 📄 database.py            # إدارة قاعدة البيانات
├── 📄 ui_components.py       # مكونات الواجهة
├── 📄 data_manager.py        # إدارة البيانات والاستيراد
├── 📄 year_transfer.py       # نقل البيانات بين السنوات
└── 📄 requirements.txt       # المتطلبات
```

## 🎯 الاستخدام

### البدء السريع
1. شغل التطبيق: `python simple_app.py`
2. ستظهر واجهة المستخدم مع بيانات تجريبية
3. استخدم البحث والتصفية لاستكشاف البيانات

### الميزات الرئيسية
- **➕ إضافة طالب**: إضافة طلاب جدد
- **📥 استيراد بيانات**: من Excel/CSV
- **📤 تصدير بيانات**: إلى Excel
- **🔄 نقل سنة دراسية**: نقل البيانات بين السنوات
- **📊 التقارير**: تقارير مالية شاملة

## 🔐 قاعدة البيانات (Supabase)

### الاتصال
البيانات محفوظة في config.py

### الجداول الرئيسية
- `students`: بيانات الطلاب الكاملة
- `payments`: تفاصيل المدفوعات
- `discounts`: أنواع الخصومات
- `fees_structure`: هيكل الرسوم

## 🛠️ التطوير

### إضافة ميزات جديدة
1. عدل الملف المناسب
2. اختبر الميزة
3. حدث التوثيق

### هيكل الكود
- `config.py`: الإعدادات والثوابت
- `database.py`: عمليات قاعدة البيانات
- `ui_components.py`: مكونات الواجهة
- `main.py`: التطبيق الرئيسي

## 📞 الدعم

للمساعدة أو الإبلاغ عن مشاكل، يرجى التواصل معنا.

---

**تم تطويره بـ ❤️ للمدارس العربية**