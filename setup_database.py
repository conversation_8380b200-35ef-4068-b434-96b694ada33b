# -*- coding: utf-8 -*-
"""
إعداد قاعدة البيانات - Supabase
Database Setup Script for School Financial Management System
"""

import asyncio
from database import DatabaseManager
from config import SUPABASE_CONFIG
import logging

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_students_table_sql():
    """SQL لإنشاء جدول الطلاب"""
    return """
    CREATE TABLE IF NOT EXISTS students (
        id SERIAL PRIMARY KEY,
        serial_number VARCHAR(50) UNIQUE,
        name VARCHAR(255) NOT NULL,
        class VARCHAR(100),
        stage VARCHAR(100),
        batch_number VARCHAR(50),
        academic_year VARCHAR(20) DEFAULT '2024/2025',
        
        -- رسوم فتح الملف
        file_opening_fee DECIMAL(10,2) DEFAULT 0,
        file_opening_date DATE,
        
        -- ر<PERSON><PERSON> الملف المحول
        transfer_file_fee DECIMAL(10,2) DEFAULT 0,
        transfer_file_date DATE,
        
        -- المديونية السابقة
        previous_debt_2324 DECIMAL(10,2) DEFAULT 0,
        
        -- مصروفات عام 23/24
        expenses_2324 DECIMAL(10,2) DEFAULT 0,
        
        -- المصروفات الدراسية 2025
        tuition_fees_2025 DECIMAL(10,2) DEFAULT 0,
        
        -- الأقساط التسعة
        installment_1_amount DECIMAL(10,2) DEFAULT 0,
        installment_1_date DATE,
        installment_1_receipt VARCHAR(100),
        
        installment_2_amount DECIMAL(10,2) DEFAULT 0,
        installment_2_date DATE,
        installment_2_receipt VARCHAR(100),
        
        installment_3_amount DECIMAL(10,2) DEFAULT 0,
        installment_3_date DATE,
        installment_3_receipt VARCHAR(100),
        
        installment_4_amount DECIMAL(10,2) DEFAULT 0,
        installment_4_date DATE,
        installment_4_receipt VARCHAR(100),
        
        installment_5_amount DECIMAL(10,2) DEFAULT 0,
        installment_5_date DATE,
        installment_5_receipt VARCHAR(100),
        
        installment_6_amount DECIMAL(10,2) DEFAULT 0,
        installment_6_date DATE,
        installment_6_receipt VARCHAR(100),
        
        installment_7_amount DECIMAL(10,2) DEFAULT 0,
        installment_7_date DATE,
        installment_7_receipt VARCHAR(100),
        
        installment_8_amount DECIMAL(10,2) DEFAULT 0,
        installment_8_date DATE,
        installment_8_receipt VARCHAR(100),
        
        installment_9_amount DECIMAL(10,2) DEFAULT 0,
        installment_9_date DATE,
        installment_9_receipt VARCHAR(100),
        
        -- إجمالي المدفوعات
        total_paid DECIMAL(10,2) DEFAULT 0,
        
        -- المبلغ المتبقي
        remaining_amount DECIMAL(10,2) DEFAULT 0,
        
        -- أنواع الخصومات
        discount_siblings DECIMAL(10,2) DEFAULT 0,
        discount_cash DECIMAL(10,2) DEFAULT 0,
        discount_teacher DECIMAL(10,2) DEFAULT 0,
        discount_transfer DECIMAL(10,2) DEFAULT 0,
        discount_personal DECIMAL(10,2) DEFAULT 0,
        
        -- إجمالي الخصومات
        total_discounts DECIMAL(10,2) DEFAULT 0,
        
        -- إجمالي الرسوم
        total_fees DECIMAL(10,2) DEFAULT 0,
        
        -- ملاحظات
        notes TEXT,
        
        -- تواريخ النظام
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    -- إنشاء فهارس للبحث السريع
    CREATE INDEX IF NOT EXISTS idx_students_name ON students(name);
    CREATE INDEX IF NOT EXISTS idx_students_stage ON students(stage);
    CREATE INDEX IF NOT EXISTS idx_students_class ON students(class);
    CREATE INDEX IF NOT EXISTS idx_students_academic_year ON students(academic_year);
    CREATE INDEX IF NOT EXISTS idx_students_serial_number ON students(serial_number);
    """

def create_fees_structure_table_sql():
    """SQL لإنشاء جدول هيكل الرسوم"""
    return """
    CREATE TABLE IF NOT EXISTS fees_structure (
        id SERIAL PRIMARY KEY,
        stage VARCHAR(100) NOT NULL,
        academic_year VARCHAR(20) NOT NULL,
        tuition_fees DECIMAL(10,2) NOT NULL,
        file_opening_fee DECIMAL(10,2) DEFAULT 0,
        transfer_file_fee DECIMAL(10,2) DEFAULT 0,
        installments_count INTEGER DEFAULT 9,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        
        UNIQUE(stage, academic_year)
    );
    """

def create_payments_table_sql():
    """SQL لإنشاء جدول المدفوعات"""
    return """
    CREATE TABLE IF NOT EXISTS payments (
        id SERIAL PRIMARY KEY,
        student_id INTEGER REFERENCES students(id) ON DELETE CASCADE,
        payment_type VARCHAR(50) NOT NULL, -- installment, file_opening, transfer_file
        installment_number INTEGER, -- للأقساط فقط
        amount DECIMAL(10,2) NOT NULL,
        payment_date DATE NOT NULL,
        receipt_number VARCHAR(100),
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    CREATE INDEX IF NOT EXISTS idx_payments_student_id ON payments(student_id);
    CREATE INDEX IF NOT EXISTS idx_payments_date ON payments(payment_date);
    CREATE INDEX IF NOT EXISTS idx_payments_type ON payments(payment_type);
    """

def create_discounts_table_sql():
    """SQL لإنشاء جدول الخصومات"""
    return """
    CREATE TABLE IF NOT EXISTS discounts (
        id SERIAL PRIMARY KEY,
        student_id INTEGER REFERENCES students(id) ON DELETE CASCADE,
        discount_type VARCHAR(50) NOT NULL, -- siblings, cash, teacher, transfer, personal
        amount DECIMAL(10,2) NOT NULL,
        percentage DECIMAL(5,2),
        description TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    CREATE INDEX IF NOT EXISTS idx_discounts_student_id ON discounts(student_id);
    CREATE INDEX IF NOT EXISTS idx_discounts_type ON discounts(discount_type);
    """

def create_academic_years_table_sql():
    """SQL لإنشاء جدول السنوات الدراسية"""
    return """
    CREATE TABLE IF NOT EXISTS academic_years (
        id SERIAL PRIMARY KEY,
        year_name VARCHAR(20) UNIQUE NOT NULL,
        start_date DATE,
        end_date DATE,
        is_current BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    """

def insert_default_data_sql():
    """SQL لإدراج البيانات الافتراضية"""
    return """
    -- إدراج السنوات الدراسية الافتراضية
    INSERT INTO academic_years (year_name, is_current) VALUES 
    ('2023/2024', FALSE),
    ('2024/2025', TRUE),
    ('2025/2026', FALSE)
    ON CONFLICT (year_name) DO NOTHING;
    
    -- إدراج هيكل الرسوم الافتراضي
    INSERT INTO fees_structure (stage, academic_year, tuition_fees, file_opening_fee, transfer_file_fee) VALUES 
    ('ابتدائي', '2024/2025', 15000, 500, 300),
    ('إعدادي', '2024/2025', 18000, 600, 400),
    ('ثانوي', '2024/2025', 22000, 700, 500),
    ('ابتدائي', '2025/2026', 16000, 550, 350),
    ('إعدادي', '2025/2026', 19000, 650, 450),
    ('ثانوي', '2025/2026', 23000, 750, 550)
    ON CONFLICT (stage, academic_year) DO NOTHING;
    """

def setup_database():
    """إعداد قاعدة البيانات الكاملة"""
    try:
        print("🔗 الاتصال بقاعدة البيانات...")
        db_manager = DatabaseManager()
        
        if not db_manager.test_connection():
            print("❌ فشل الاتصال بقاعدة البيانات!")
            return False
        
        print("✅ تم الاتصال بنجاح!")
        
        # إنشاء الجداول
        tables_sql = [
            ("جدول الطلاب", create_students_table_sql()),
            ("جدول هيكل الرسوم", create_fees_structure_table_sql()),
            ("جدول المدفوعات", create_payments_table_sql()),
            ("جدول الخصومات", create_discounts_table_sql()),
            ("جدول السنوات الدراسية", create_academic_years_table_sql()),
        ]
        
        for table_name, sql in tables_sql:
            try:
                print(f"📋 إنشاء {table_name}...")
                # هنا يجب استخدام Supabase API لتنفيذ SQL
                # db_manager.supabase.postgrest.rpc('execute_sql', {'sql': sql})
                print(f"✅ تم إنشاء {table_name} بنجاح!")
            except Exception as e:
                print(f"❌ خطأ في إنشاء {table_name}: {e}")
        
        # إدراج البيانات الافتراضية
        try:
            print("📊 إدراج البيانات الافتراضية...")
            # db_manager.supabase.postgrest.rpc('execute_sql', {'sql': insert_default_data_sql()})
            print("✅ تم إدراج البيانات الافتراضية بنجاح!")
        except Exception as e:
            print(f"❌ خطأ في إدراج البيانات الافتراضية: {e}")
        
        print("\n🎉 تم إعداد قاعدة البيانات بنجاح!")
        print("يمكنك الآن تشغيل التطبيق باستخدام: python main.py")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في إعداد قاعدة البيانات: {e}")
        return False

def print_sql_commands():
    """طباعة أوامر SQL للتنفيذ اليدوي"""
    print("\n" + "="*60)
    print("📋 أوامر SQL للتنفيذ اليدوي في Supabase")
    print("="*60)
    
    print("\n-- 1. جدول الطلاب")
    print(create_students_table_sql())
    
    print("\n-- 2. جدول هيكل الرسوم")
    print(create_fees_structure_table_sql())
    
    print("\n-- 3. جدول المدفوعات")
    print(create_payments_table_sql())
    
    print("\n-- 4. جدول الخصومات")
    print(create_discounts_table_sql())
    
    print("\n-- 5. جدول السنوات الدراسية")
    print(create_academic_years_table_sql())
    
    print("\n-- 6. البيانات الافتراضية")
    print(insert_default_data_sql())
    
    print("\n" + "="*60)
    print("انسخ والصق هذه الأوامر في SQL Editor في Supabase")
    print("="*60)

def main():
    """الدالة الرئيسية"""
    print("🏫 إعداد قاعدة البيانات - نظام إدارة البيانات المالية للمدرسة")
    print("="*70)
    
    print("\nاختر نوع الإعداد:")
    print("1. إعداد تلقائي (يتطلب اتصال بـ Supabase)")
    print("2. عرض أوامر SQL للتنفيذ اليدوي")
    print("3. اختبار الاتصال فقط")
    print("4. خروج")
    
    choice = input("\nاختر رقم (1-4): ").strip()
    
    if choice == "1":
        setup_database()
    elif choice == "2":
        print_sql_commands()
    elif choice == "3":
        try:
            db_manager = DatabaseManager()
            if db_manager.test_connection():
                print("✅ الاتصال بقاعدة البيانات يعمل بشكل صحيح!")
            else:
                print("❌ فشل الاتصال بقاعدة البيانات!")
        except Exception as e:
            print(f"❌ خطأ في الاتصال: {e}")
    elif choice == "4":
        print("👋 وداعاً!")
    else:
        print("❌ اختيار غير صحيح!")

if __name__ == "__main__":
    main()
