@echo off
echo 🏫 نظام إدارة البيانات المالية للمدرسة
echo =====================================
echo.
echo اختر نوع التشغيل:
echo 1. التشغيل السريع (النسخة المبسطة)
echo 2. التشغيل الكامل (مع جميع الميزات)
echo 3. تثبيت المتطلبات
echo 4. خروج
echo.
set /p choice="اختر رقم (1-4): "

if "%choice%"=="1" (
    echo.
    echo 🚀 تشغيل النسخة المبسطة...
    python simple_app.py
) else if "%choice%"=="2" (
    echo.
    echo 🚀 تشغيل النسخة الكاملة...
    python main.py
) else if "%choice%"=="3" (
    echo.
    echo 📦 تثبيت المتطلبات...
    pip install -r requirements.txt
    echo.
    echo ✅ تم تثبيت المتطلبات بنجاح!
    pause
) else if "%choice%"=="4" (
    echo.
    echo 👋 وداعاً!
    exit
) else (
    echo.
    echo ❌ اختيار غير صحيح!
    pause
    goto start
)

pause
