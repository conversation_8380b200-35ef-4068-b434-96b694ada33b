# -*- coding: utf-8 -*-
"""
إعدادات التطبيق الرئيسية
School Financial Management System Configuration
"""

import os
from typing import Dict, Any

# 🔐 إعدادات Supabase
SUPABASE_CONFIG = {
    'url': 'https://bbigwqwtmhctqrptkuni.supabase.co',
    'anon_key': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyOTg3MjYsImV4cCI6MjA2MTg3NDcyNn0.k6_MpI7ohitVroAuc2e5nYpAdFRphn7185_u_sm_ZVM',
    'service_key': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJiaWd3cXd0bWhjdHFycHRrdW5pIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjI5ODcyNiwiZXhwIjoyMDYxODc0NzI2fQ.CWaGhsn91tSxfRRiJi1IGiDsvMHmZdX7ril1Q8AOcHA'
}

# 🎨 ألوان التطبيق
COLORS = {
    'primary': '#2E86AB',      # أزرق أساسي
    'secondary': '#A23B72',    # وردي ثانوي
    'success': '#F18F01',      # برتقالي للنجاح
    'warning': '#C73E1D',      # أحمر للتحذير
    'info': '#4ECDC4',         # تركوازي للمعلومات
    'light': '#F8F9FA',        # رمادي فاتح
    'dark': '#343A40',         # رمادي داكن
    'white': '#FFFFFF',        # أبيض
    'background': '#F5F7FA',   # خلفية التطبيق
    'sidebar': '#2C3E50',      # لون الشريط الجانبي
    'table_header': '#34495E', # رأس الجدول
    'table_row1': '#ECF0F1',   # صف الجدول الأول
    'table_row2': '#FFFFFF',   # صف الجدول الثاني
}

# 📝 خطوط التطبيق
FONTS = {
    'title': ('Arial', 16, 'bold'),
    'subtitle': ('Arial', 12, 'bold'),
    'normal': ('Arial', 10),
    'small': ('Arial', 8),
    'table_header': ('Arial', 10, 'bold'),
    'table_data': ('Arial', 9),
}

# 🏫 إعدادات المدرسة
SCHOOL_SETTINGS = {
    'name': 'مدرسة المستقبل الخاصة',
    'academic_years': ['2023/2024', '2024/2025', '2025/2026'],
    'current_year': '2024/2025',
    'grades': {
        'ابتدائي': ['الأول', 'الثاني', 'الثالث', 'الرابع', 'الخامس', 'السادس'],
        'إعدادي': ['الأول', 'الثاني', 'الثالث'],
        'ثانوي': ['الأول', 'الثاني', 'الثالث']
    },
    'installments_count': 9,  # عدد الأقساط
}

# 💰 هيكل الرسوم الافتراضي
DEFAULT_FEES = {
    'ابتدائي': {
        'tuition': 15000,
        'file_opening': 500,
        'transfer_file': 300,
        'installments': 9
    },
    'إعدادي': {
        'tuition': 18000,
        'file_opening': 600,
        'transfer_file': 400,
        'installments': 9
    },
    'ثانوي': {
        'tuition': 22000,
        'file_opening': 700,
        'transfer_file': 500,
        'installments': 9
    }
}

# 🎯 أنواع الخصومات
DISCOUNT_TYPES = {
    'siblings': {'name': 'خصم الأخوة', 'percentage': 10, 'emoji': '👨‍👩‍👧‍👦'},
    'cash': {'name': 'خصم الدفع المقدم', 'percentage': 5, 'emoji': '💰'},
    'teacher': {'name': 'خصم المدرسين', 'percentage': 50, 'emoji': '👨‍🏫'},
    'transfer': {'name': 'خصم التحويل', 'percentage': 15, 'emoji': '🔄'},
    'personal': {'name': 'خصم شخصي', 'percentage': 0, 'emoji': '⭐'}
}

# 📊 إعدادات الجدول
TABLE_CONFIG = {
    'row_height': 25,
    'header_height': 30,
    'column_widths': {
        'serial': 80,
        'name': 200,
        'class': 100,
        'stage': 100,
        'batch': 80,
        'fees': 120,
        'paid': 120,
        'remaining': 120,
        'discount': 100,
        'notes': 150
    }
}

# 🔧 إعدادات التطبيق العامة
APP_CONFIG = {
    'window_title': '🏫 نظام إدارة البيانات المالية للمدرسة',
    'window_size': '1400x900',
    'min_window_size': (1200, 700),
    'icon_path': 'assets/school_icon.ico',
    'backup_interval': 30,  # دقائق
    'auto_save': True,
    'language': 'ar',
    'theme': 'modern'
}

# 📁 مسارات الملفات
PATHS = {
    'exports': 'exports/',
    'imports': 'imports/',
    'backups': 'backups/',
    'templates': 'templates/',
    'logs': 'logs/'
}

# 🚨 رسائل النظام
MESSAGES = {
    'success': {
        'data_imported': '✅ تم استيراد البيانات بنجاح!',
        'data_exported': '✅ تم تصدير البيانات بنجاح!',
        'year_transferred': '✅ تم نقل البيانات للسنة الجديدة بنجاح!',
        'student_added': '✅ تم إضافة الطالب بنجاح!',
        'payment_recorded': '✅ تم تسجيل الدفعة بنجاح!'
    },
    'warning': {
        'unsaved_changes': '⚠️ يوجد تغييرات غير محفوظة!',
        'duplicate_student': '⚠️ الطالب موجود مسبقاً!',
        'invalid_amount': '⚠️ المبلغ المدخل غير صحيح!'
    },
    'error': {
        'connection_failed': '❌ فشل الاتصال بقاعدة البيانات!',
        'file_not_found': '❌ الملف غير موجود!',
        'invalid_data': '❌ البيانات المدخلة غير صحيحة!'
    },
    'info': {
        'loading': '⏳ جاري التحميل...',
        'processing': '⚙️ جاري المعالجة...',
        'connecting': '🔗 جاري الاتصال...'
    }
}

# 🎭 الرموز التعبيرية للواجهة
EMOJIS = {
    'student': '👨‍🎓',
    'money': '💰',
    'payment': '💳',
    'discount': '🏷️',
    'report': '📊',
    'export': '📤',
    'import': '📥',
    'settings': '⚙️',
    'search': '🔍',
    'add': '➕',
    'edit': '✏️',
    'delete': '🗑️',
    'save': '💾',
    'refresh': '🔄',
    'print': '🖨️',
    'calendar': '📅',
    'calculator': '🧮',
    'chart': '📈',
    'database': '🗄️',
    'backup': '💾',
    'restore': '♻️'
}

def get_color(color_name: str) -> str:
    """الحصول على لون من قاموس الألوان"""
    return COLORS.get(color_name, COLORS['primary'])

def get_font(font_name: str) -> tuple:
    """الحصول على خط من قاموس الخطوط"""
    return FONTS.get(font_name, FONTS['normal'])

def get_emoji(emoji_name: str) -> str:
    """الحصول على رمز تعبيري"""
    return EMOJIS.get(emoji_name, '📝')

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    for path in PATHS.values():
        os.makedirs(path, exist_ok=True)
