# -*- coding: utf-8 -*-
"""
نظام إدارة البيانات المالية للمدرسة - نسخة مبسطة
School Financial Management System - Simple Version
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
from datetime import datetime
from typing import Dict, List, Optional

class SimpleSchoolFinanceApp:
    """التطبيق المبسط لإدارة البيانات المالية للمدرسة"""
    
    def __init__(self):
        """تهيئة التطبيق"""
        self.setup_window()
        self.setup_data()
        self.setup_ui()
        self.load_sample_data()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root = tk.Tk()
        self.root.title("🏫 نظام إدارة البيانات المالية للمدرسة")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 700)
        
        # توسيط النافذة
        self.center_window()
        
        # ألوان التطبيق
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'success': '#F18F01',
            'warning': '#C73E1D',
            'info': '#4ECDC4',
            'light': '#F8F9FA',
            'dark': '#343A40',
            'sidebar': '#2C3E50'
        }
        
        # إعداد الشبكة
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(1, weight=1)
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = 1400
        height = 900
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_data(self):
        """إعداد البيانات"""
        self.students_data = []
        self.filtered_data = []
        self.current_year = "2024/2025"
        self.academic_years = ["2023/2024", "2024/2025", "2025/2026"]
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الشريط الجانبي
        self.setup_sidebar()
        
        # المنطقة الرئيسية
        self.setup_main_area()
        
        # شريط الحالة
        self.setup_status_bar()
    
    def setup_sidebar(self):
        """إعداد الشريط الجانبي"""
        self.sidebar = tk.Frame(self.root, bg=self.colors['sidebar'], width=280)
        self.sidebar.grid(row=0, column=0, sticky="nsew")
        self.sidebar.grid_propagate(False)
        
        # عنوان التطبيق
        title_label = tk.Label(
            self.sidebar,
            text="🏫 إدارة البيانات المالية",
            font=('Arial', 16, 'bold'),
            bg=self.colors['sidebar'],
            fg='white',
            pady=20
        )
        title_label.pack()
        
        # معلومات السنة الدراسية
        year_frame = tk.Frame(self.sidebar, bg=self.colors['sidebar'])
        year_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Label(
            year_frame,
            text="📅 السنة الدراسية:",
            font=('Arial', 12),
            bg=self.colors['sidebar'],
            fg='white'
        ).pack()
        
        self.year_var = tk.StringVar(value=self.current_year)
        year_combo = ttk.Combobox(
            year_frame,
            textvariable=self.year_var,
            values=self.academic_years,
            state='readonly'
        )
        year_combo.pack(pady=5, fill='x')
        year_combo.bind('<<ComboboxSelected>>', self.on_year_change)
        
        # الإحصائيات
        self.setup_statistics()
        
        # أزرار العمليات
        self.setup_action_buttons()
    
    def setup_statistics(self):
        """إعداد الإحصائيات"""
        stats_frame = tk.Frame(self.sidebar, bg=self.colors['sidebar'])
        stats_frame.pack(fill='x', padx=20, pady=20)
        
        tk.Label(
            stats_frame,
            text="📊 إحصائيات سريعة",
            font=('Arial', 14, 'bold'),
            bg=self.colors['sidebar'],
            fg='white'
        ).pack(pady=(0, 10))
        
        # إحصائيات ديناميكية
        self.stats_labels = {}
        stats_items = [
            ('total_students', '👨‍🎓 إجمالي الطلاب', '0'),
            ('total_fees', '💰 إجمالي الرسوم', '0.00'),
            ('total_paid', '✅ إجمالي المدفوع', '0.00'),
            ('total_remaining', '⏳ المتبقي', '0.00'),
            ('collection_rate', '📈 نسبة التحصيل', '0%')
        ]
        
        for key, label, default in stats_items:
            stat_frame = tk.Frame(stats_frame, bg=self.colors['sidebar'])
            stat_frame.pack(fill='x', pady=2)
            
            tk.Label(
                stat_frame,
                text=label,
                font=('Arial', 9),
                bg=self.colors['sidebar'],
                fg='white',
                anchor='w'
            ).pack(side='left')
            
            self.stats_labels[key] = tk.Label(
                stat_frame,
                text=default,
                font=('Arial', 9, 'bold'),
                bg=self.colors['sidebar'],
                fg='yellow',
                anchor='e'
            )
            self.stats_labels[key].pack(side='right')
    
    def setup_action_buttons(self):
        """إعداد أزرار العمليات"""
        buttons_frame = tk.Frame(self.sidebar, bg=self.colors['sidebar'])
        buttons_frame.pack(fill='x', padx=20, pady=20)
        
        tk.Label(
            buttons_frame,
            text="⚡ العمليات السريعة",
            font=('Arial', 12, 'bold'),
            bg=self.colors['sidebar'],
            fg='white'
        ).pack(pady=(0, 10))
        
        # أزرار العمليات
        buttons = [
            ("➕ إضافة طالب", self.add_student, self.colors['success']),
            ("📥 استيراد بيانات", self.import_data, self.colors['info']),
            ("📤 تصدير بيانات", self.export_data, self.colors['primary']),
            ("🔄 نقل سنة دراسية", self.transfer_year, self.colors['secondary']),
            ("📊 التقارير", self.show_reports, self.colors['warning']),
            ("⚙️ الإعدادات", self.show_settings, self.colors['dark']),
        ]
        
        for text, command, color in buttons:
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=command,
                font=('Arial', 10),
                bg=color,
                fg='white',
                relief='flat',
                pady=8,
                cursor='hand2'
            )
            btn.pack(fill='x', pady=3)
            
            # تأثير hover
            btn.bind("<Enter>", lambda e, b=btn: b.config(relief='raised'))
            btn.bind("<Leave>", lambda e, b=btn: b.config(relief='flat'))
    
    def setup_main_area(self):
        """إعداد المنطقة الرئيسية"""
        self.main_frame = tk.Frame(self.root, bg='white')
        self.main_frame.grid(row=0, column=1, sticky="nsew", padx=10, pady=10)
        self.main_frame.grid_rowconfigure(1, weight=1)
        self.main_frame.grid_columnconfigure(0, weight=1)
        
        # إطار البحث
        self.setup_search_frame()
        
        # جدول البيانات
        self.setup_data_table()
    
    def setup_search_frame(self):
        """إعداد إطار البحث"""
        search_frame = tk.Frame(self.main_frame, bg='white', relief='ridge', bd=1)
        search_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        search_frame.grid_columnconfigure(1, weight=1)
        
        # عنوان البحث
        tk.Label(
            search_frame,
            text="🔍 البحث والتصفية",
            font=('Arial', 12, 'bold'),
            bg='white',
            fg=self.colors['primary']
        ).grid(row=0, column=0, columnspan=4, pady=10)
        
        # البحث النصي
        tk.Label(search_frame, text="البحث:", bg='white').grid(row=1, column=0, padx=5, pady=5, sticky='w')
        
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, font=('Arial', 10), width=30)
        search_entry.grid(row=1, column=1, padx=5, pady=5, sticky='ew')
        search_entry.bind('<KeyRelease>', self.on_search_change)
        
        # فلتر المرحلة
        tk.Label(search_frame, text="المرحلة:", bg='white').grid(row=1, column=2, padx=5, pady=5)
        
        self.stage_var = tk.StringVar(value="الكل")
        stage_combo = ttk.Combobox(
            search_frame,
            textvariable=self.stage_var,
            values=["الكل", "ابتدائي", "إعدادي", "ثانوي"],
            state='readonly',
            width=12
        )
        stage_combo.grid(row=1, column=3, padx=5, pady=5)
        stage_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # زر مسح
        clear_btn = tk.Button(
            search_frame,
            text="🗑️ مسح",
            command=self.clear_search,
            bg=self.colors['warning'],
            fg='white',
            font=('Arial', 9)
        )
        clear_btn.grid(row=1, column=4, padx=5, pady=5)
    
    def setup_data_table(self):
        """إعداد جدول البيانات"""
        # إطار الجدول
        table_frame = tk.Frame(self.main_frame, bg='white')
        table_frame.grid(row=1, column=0, sticky="nsew")
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # إنشاء Treeview
        columns = ('serial', 'name', 'class', 'stage', 'total_fees', 'total_paid', 'remaining', 'discounts', 'notes')
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)
        
        # تحديد رؤوس الأعمدة
        headers = {
            'serial': 'الرقم التسلسلي',
            'name': 'اسم الطالب',
            'class': 'الفصل',
            'stage': 'المرحلة',
            'total_fees': 'إجمالي الرسوم',
            'total_paid': 'المدفوع',
            'remaining': 'المتبقي',
            'discounts': 'الخصومات',
            'notes': 'ملاحظات'
        }
        
        widths = {
            'serial': 100,
            'name': 200,
            'class': 80,
            'stage': 100,
            'total_fees': 120,
            'total_paid': 120,
            'remaining': 120,
            'discounts': 100,
            'notes': 150
        }
        
        for col in columns:
            self.tree.heading(col, text=headers[col], anchor='center')
            self.tree.column(col, width=widths[col], anchor='center')
        
        # شريط التمرير
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=v_scrollbar.set)
        
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.tree.xview)
        self.tree.configure(xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # ربط الأحداث
        self.tree.bind('<<TreeviewSelect>>', self.on_tree_select)
        self.tree.bind('<Double-1>', self.on_tree_double_click)
        
        # تنسيق الصفوف
        self.tree.tag_configure('evenrow', background='#f0f0f0')
        self.tree.tag_configure('oddrow', background='white')
    
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = tk.Frame(self.root, bg=self.colors['light'], height=30, relief='sunken', bd=1)
        self.status_bar.grid(row=1, column=0, columnspan=2, sticky="ew")
        self.status_bar.grid_propagate(False)
        
        self.status_label = tk.Label(
            self.status_bar,
            text="🟢 جاهز",
            bg=self.colors['light'],
            fg=self.colors['dark'],
            font=('Arial', 9),
            anchor='w'
        )
        self.status_label.pack(side='left', padx=10, pady=5)
        
        self.info_label = tk.Label(
            self.status_bar,
            text="",
            bg=self.colors['light'],
            fg=self.colors['dark'],
            font=('Arial', 9),
            anchor='e'
        )
        self.info_label.pack(side='right', padx=10, pady=5)
    
    def load_sample_data(self):
        """تحميل بيانات تجريبية"""
        sample_data = [
            {
                'serial_number': '2025001',
                'name': 'أحمد محمد علي',
                'class': 'الأول',
                'stage': 'ابتدائي',
                'total_fees': 15000.00,
                'total_paid': 10000.00,
                'remaining_amount': 5000.00,
                'total_discounts': 500.00,
                'notes': 'طالب متفوق'
            },
            {
                'serial_number': '2025002',
                'name': 'فاطمة أحمد حسن',
                'class': 'الثاني',
                'stage': 'إعدادي',
                'total_fees': 18000.00,
                'total_paid': 15000.00,
                'remaining_amount': 3000.00,
                'total_discounts': 900.00,
                'notes': 'خصم الأخوة'
            },
            {
                'serial_number': '2025003',
                'name': 'محمد عبد الله',
                'class': 'الثالث',
                'stage': 'ثانوي',
                'total_fees': 22000.00,
                'total_paid': 20000.00,
                'remaining_amount': 2000.00,
                'total_discounts': 1100.00,
                'notes': 'خصم المدرسين'
            },
            {
                'serial_number': '2025004',
                'name': 'نور الهدى سالم',
                'class': 'الرابع',
                'stage': 'ابتدائي',
                'total_fees': 15000.00,
                'total_paid': 12000.00,
                'remaining_amount': 3000.00,
                'total_discounts': 750.00,
                'notes': 'دفع منتظم'
            },
            {
                'serial_number': '2025005',
                'name': 'يوسف إبراهيم',
                'class': 'الأول',
                'stage': 'إعدادي',
                'total_fees': 18000.00,
                'total_paid': 8000.00,
                'remaining_amount': 10000.00,
                'total_discounts': 0.00,
                'notes': 'متأخر في الدفع'
            }
        ]
        
        self.students_data = sample_data
        self.filtered_data = sample_data.copy()
        self.load_data_to_table()
        self.update_statistics()
        self.set_status("تم تحميل البيانات التجريبية", "success")
    
    def load_data_to_table(self):
        """تحميل البيانات في الجدول"""
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # إضافة البيانات الجديدة
        for i, student in enumerate(self.filtered_data):
            values = (
                student.get('serial_number', ''),
                student.get('name', ''),
                student.get('class', ''),
                student.get('stage', ''),
                f"{float(student.get('total_fees', 0)):,.2f}",
                f"{float(student.get('total_paid', 0)):,.2f}",
                f"{float(student.get('remaining_amount', 0)):,.2f}",
                f"{float(student.get('total_discounts', 0)):,.2f}",
                student.get('notes', '')
            )
            
            tag = 'evenrow' if i % 2 == 0 else 'oddrow'
            self.tree.insert('', 'end', values=values, tags=(tag,))
    
    def update_statistics(self):
        """تحديث الإحصائيات"""
        if not self.students_data:
            return
        
        total_students = len(self.students_data)
        total_fees = sum(float(s.get('total_fees', 0)) for s in self.students_data)
        total_paid = sum(float(s.get('total_paid', 0)) for s in self.students_data)
        total_remaining = total_fees - total_paid
        collection_rate = (total_paid / total_fees * 100) if total_fees > 0 else 0
        
        self.stats_labels['total_students'].config(text=str(total_students))
        self.stats_labels['total_fees'].config(text=f"{total_fees:,.0f}")
        self.stats_labels['total_paid'].config(text=f"{total_paid:,.0f}")
        self.stats_labels['total_remaining'].config(text=f"{total_remaining:,.0f}")
        self.stats_labels['collection_rate'].config(text=f"{collection_rate:.1f}%")
    
    def set_status(self, message: str, status_type: str = "info"):
        """تحديث شريط الحالة"""
        emoji_map = {
            'success': '🟢',
            'warning': '🟡',
            'error': '🔴',
            'info': '🔵'
        }
        
        emoji = emoji_map.get(status_type, '🔵')
        self.status_label.config(text=f"{emoji} {message}")
        
        # تحديث معلومات إضافية
        current_time = datetime.now().strftime("%H:%M:%S")
        self.info_label.config(text=f"آخر تحديث: {current_time}")
    
    # معالجات الأحداث
    def on_year_change(self, event=None):
        """عند تغيير السنة الدراسية"""
        self.current_year = self.year_var.get()
        self.set_status(f"تم تغيير السنة إلى {self.current_year}", "info")
    
    def on_search_change(self, event=None):
        """عند تغيير نص البحث"""
        self.apply_filters()
    
    def on_filter_change(self, event=None):
        """عند تغيير الفلاتر"""
        self.apply_filters()
    
    def apply_filters(self):
        """تطبيق البحث والفلاتر"""
        search_term = self.search_var.get().lower()
        stage_filter = self.stage_var.get()
        
        filtered = []
        for student in self.students_data:
            # تطبيق البحث النصي
            if search_term and search_term not in student.get('name', '').lower():
                continue
            
            # تطبيق فلتر المرحلة
            if stage_filter != "الكل" and student.get('stage') != stage_filter:
                continue
            
            filtered.append(student)
        
        self.filtered_data = filtered
        self.load_data_to_table()
        self.set_status(f"تم العثور على {len(filtered)} من أصل {len(self.students_data)} طالب", "info")
    
    def clear_search(self):
        """مسح البحث والفلاتر"""
        self.search_var.set("")
        self.stage_var.set("الكل")
        self.filtered_data = self.students_data.copy()
        self.load_data_to_table()
        self.set_status("تم مسح الفلاتر", "info")
    
    def on_tree_select(self, event):
        """عند تحديد صف في الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            student_name = item['values'][1] if item['values'] else ""
            self.set_status(f"تم تحديد الطالب: {student_name}", "info")
    
    def on_tree_double_click(self, event):
        """عند النقر المزدوج على صف"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            student_name = item['values'][1] if item['values'] else ""
            messagebox.showinfo("تفاصيل الطالب", f"سيتم فتح نافذة تفاصيل الطالب: {student_name}")
    
    # العمليات الرئيسية
    def add_student(self):
        """إضافة طالب جديد"""
        messagebox.showinfo("إضافة طالب", "🚧 قريباً: نافذة إضافة طالب جديد")
    
    def import_data(self):
        """استيراد البيانات"""
        messagebox.showinfo("استيراد البيانات", "🚧 قريباً: استيراد البيانات من Excel/CSV")
    
    def export_data(self):
        """تصدير البيانات"""
        messagebox.showinfo("تصدير البيانات", "🚧 قريباً: تصدير البيانات إلى Excel")
    
    def transfer_year(self):
        """نقل السنة الدراسية"""
        messagebox.showinfo("نقل السنة", "🚧 قريباً: نقل البيانات للسنة الدراسية الجديدة")
    
    def show_reports(self):
        """عرض التقارير"""
        messagebox.showinfo("التقارير", "🚧 قريباً: التقارير المالية والإحصائيات")
    
    def show_settings(self):
        """عرض الإعدادات"""
        messagebox.showinfo("الإعدادات", "🚧 قريباً: إعدادات التطبيق")
    
    def run(self):
        """تشغيل التطبيق"""
        self.set_status("التطبيق جاهز للاستخدام", "success")
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        app = SimpleSchoolFinanceApp()
        app.run()
    except Exception as e:
        messagebox.showerror("خطأ", f"خطأ في تشغيل التطبيق: {e}")

if __name__ == "__main__":
    main()
