# -*- coding: utf-8 -*-
"""
مدير نقل البيانات بين السنوات الدراسية
School Financial Management System - Year Transfer Manager
"""

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from typing import Dict, List, Optional
from datetime import datetime
import threading
import logging
from config import SCHOOL_SETTINGS, DEFAULT_FEES, COLORS, FONTS, get_color, get_font

logger = logging.getLogger(__name__)

class YearTransferManager:
    """مدير نقل البيانات بين السنوات الدراسية"""
    
    def __init__(self, db_manager):
        """تهيئة مدير النقل"""
        self.db_manager = db_manager
        self.transfer_options = {
            'reset_payments': True,
            'keep_discounts': True,
            'update_fees': True,
            'transfer_debts': True,
            'upgrade_grades': True
        }
    
    def show_transfer_dialog(self, parent=None) -> bool:
        """عرض نافذة نقل السنة الدراسية"""
        dialog = YearTransferDialog(parent, self)
        return dialog.show()
    
    def transfer_students(self, source_year: str, target_year: str, options: Dict) -> bool:
        """نقل الطلاب من سنة إلى أخرى"""
        try:
            # الحصول على طلاب السنة المصدر
            students = self.db_manager.get_all_students(source_year)
            
            if not students:
                messagebox.showwarning("تحذير", f"لا يوجد طلاب في السنة {source_year}")
                return False
            
            # معاينة التغييرات
            preview_data = self._prepare_transfer_preview(students, target_year, options)
            
            # عرض نافذة المعاينة
            if self._show_preview_dialog(preview_data):
                # تنفيذ النقل
                success_count = self._execute_transfer(students, target_year, options)
                
                if success_count > 0:
                    messagebox.showinfo(
                        "نجح النقل",
                        f"تم نقل {success_count} طالب بنجاح إلى السنة {target_year}"
                    )
                    return True
                else:
                    messagebox.showerror("خطأ", "فشل في نقل البيانات")
                    return False
            
            return False
            
        except Exception as e:
            logger.error(f"خطأ في نقل البيانات: {e}")
            messagebox.showerror("خطأ", f"خطأ في نقل البيانات: {e}")
            return False
    
    def _prepare_transfer_preview(self, students: List[Dict], target_year: str, options: Dict) -> List[Dict]:
        """تحضير معاينة النقل"""
        preview_data = []
        
        for student in students:
            new_student = self._prepare_student_for_transfer(student, target_year, options)
            
            # إضافة معلومات المقارنة
            comparison = {
                'original': student,
                'new': new_student,
                'changes': self._get_changes_summary(student, new_student)
            }
            
            preview_data.append(comparison)
        
        return preview_data
    
    def _prepare_student_for_transfer(self, student: Dict, target_year: str, options: Dict) -> Dict:
        """تحضير بيانات الطالب للنقل"""
        new_student = student.copy()
        
        # تحديث السنة الدراسية
        new_student['academic_year'] = target_year
        
        # ترقية المرحلة والفصل
        if options.get('upgrade_grades', True):
            new_student = self._upgrade_student_grade(new_student)
        
        # معالجة المديونيات
        if options.get('transfer_debts', True):
            remaining = float(student.get('remaining_amount', 0))
            new_student['previous_debt'] = remaining
        else:
            new_student['previous_debt'] = 0
        
        # إعادة تصفير المدفوعات
        if options.get('reset_payments', True):
            new_student['total_paid'] = 0
            # إعادة تصفير الأقساط
            for i in range(1, 10):
                new_student[f'installment_{i}_amount'] = 0
                new_student[f'installment_{i}_date'] = None
                new_student[f'installment_{i}_receipt'] = None
        
        # تحديث الرسوم
        if options.get('update_fees', True):
            new_student = self._update_student_fees(new_student)
        
        # معالجة الخصومات
        if not options.get('keep_discounts', True):
            new_student['total_discounts'] = 0
            new_student['discount_siblings'] = 0
            new_student['discount_cash'] = 0
            new_student['discount_teacher'] = 0
            new_student['discount_transfer'] = 0
            new_student['discount_personal'] = 0
        
        # حساب المبلغ المتبقي الجديد
        total_fees = float(new_student.get('total_fees', 0))
        total_paid = float(new_student.get('total_paid', 0))
        total_discounts = float(new_student.get('total_discounts', 0))
        previous_debt = float(new_student.get('previous_debt', 0))
        
        new_student['remaining_amount'] = max(0, total_fees + previous_debt - total_paid - total_discounts)
        
        # إزالة المعرف للإدراج كسجل جديد
        new_student.pop('id', None)
        new_student.pop('created_at', None)
        new_student['updated_at'] = datetime.now().isoformat()
        
        return new_student
    
    def _upgrade_student_grade(self, student: Dict) -> Dict:
        """ترقية المرحلة والفصل الدراسي"""
        current_stage = student.get('stage', '')
        current_class = student.get('class', '')
        
        # قواعد الترقية
        grade_progression = {
            'ابتدائي': {
                'الأول': 'الثاني',
                'الثاني': 'الثالث',
                'الثالث': 'الرابع',
                'الرابع': 'الخامس',
                'الخامس': 'السادس',
                'السادس': {'stage': 'إعدادي', 'class': 'الأول'}
            },
            'إعدادي': {
                'الأول': 'الثاني',
                'الثاني': 'الثالث',
                'الثالث': {'stage': 'ثانوي', 'class': 'الأول'}
            },
            'ثانوي': {
                'الأول': 'الثاني',
                'الثاني': 'الثالث',
                'الثالث': 'متخرج'  # أو يمكن إزالة الطالب
            }
        }
        
        if current_stage in grade_progression and current_class in grade_progression[current_stage]:
            progression = grade_progression[current_stage][current_class]
            
            if isinstance(progression, dict):
                # انتقال بين المراحل
                student['stage'] = progression['stage']
                student['class'] = progression['class']
            else:
                # ترقية داخل نفس المرحلة
                student['class'] = progression
        
        return student
    
    def _update_student_fees(self, student: Dict) -> Dict:
        """تحديث رسوم الطالب حسب المرحلة الجديدة"""
        stage = student.get('stage', '')
        
        if stage in DEFAULT_FEES:
            fees_structure = DEFAULT_FEES[stage]
            
            student['tuition_fees'] = fees_structure['tuition']
            student['file_opening_fee'] = fees_structure.get('file_opening', 0)
            student['transfer_file_fee'] = fees_structure.get('transfer_file', 0)
            
            # حساب إجمالي الرسوم
            total_fees = (
                fees_structure['tuition'] +
                fees_structure.get('file_opening', 0) +
                fees_structure.get('transfer_file', 0)
            )
            student['total_fees'] = total_fees
        
        return student
    
    def _get_changes_summary(self, original: Dict, new: Dict) -> List[str]:
        """الحصول على ملخص التغييرات"""
        changes = []
        
        # مقارنة الحقول المهمة
        important_fields = {
            'stage': 'المرحلة',
            'class': 'الفصل',
            'total_fees': 'إجمالي الرسوم',
            'previous_debt': 'المديونية السابقة',
            'remaining_amount': 'المبلغ المتبقي'
        }
        
        for field, label in important_fields.items():
            old_value = original.get(field, '')
            new_value = new.get(field, '')
            
            if str(old_value) != str(new_value):
                if field in ['total_fees', 'previous_debt', 'remaining_amount']:
                    changes.append(f"{label}: {old_value:,.2f} ← {new_value:,.2f}")
                else:
                    changes.append(f"{label}: {old_value} ← {new_value}")
        
        return changes
    
    def _show_preview_dialog(self, preview_data: List[Dict]) -> bool:
        """عرض نافذة معاينة التغييرات"""
        dialog = TransferPreviewDialog(None, preview_data)
        return dialog.show()
    
    def _execute_transfer(self, students: List[Dict], target_year: str, options: Dict) -> int:
        """تنفيذ عملية النقل"""
        success_count = 0
        
        for student in students:
            try:
                new_student = self._prepare_student_for_transfer(student, target_year, options)
                
                if self.db_manager.add_student(new_student):
                    success_count += 1
                else:
                    logger.warning(f"فشل في نقل الطالب: {student.get('name', 'غير محدد')}")
                    
            except Exception as e:
                logger.error(f"خطأ في نقل الطالب {student.get('name', 'غير محدد')}: {e}")
        
        return success_count

class YearTransferDialog:
    """نافذة نقل السنة الدراسية"""
    
    def __init__(self, parent, transfer_manager: YearTransferManager):
        self.parent = parent
        self.transfer_manager = transfer_manager
        self.result = False
        
        self.dialog = tk.Toplevel(parent) if parent else tk.Tk()
        self.setup_dialog()
    
    def setup_dialog(self):
        """إعداد نافذة الحوار"""
        self.dialog.title("🔄 نقل السنة الدراسية")
        self.dialog.geometry("500x600")
        self.dialog.resizable(False, False)
        
        # توسيط النافذة
        self.center_window()
        
        # إطار العنوان
        title_frame = tk.Frame(self.dialog, bg=get_color('primary'), height=60)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame,
            text="🔄 نقل البيانات للسنة الدراسية الجديدة",
            font=get_font('title'),
            bg=get_color('primary'),
            fg='white'
        )
        title_label.pack(expand=True)
        
        # إطار المحتوى
        content_frame = tk.Frame(self.dialog, padx=20, pady=20)
        content_frame.pack(fill='both', expand=True)
        
        # اختيار السنوات
        self.setup_year_selection(content_frame)
        
        # خيارات النقل
        self.setup_transfer_options(content_frame)
        
        # أزرار التحكم
        self.setup_control_buttons(content_frame)
    
    def center_window(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (250)
        y = (self.dialog.winfo_screenheight() // 2) - (300)
        self.dialog.geometry(f"500x600+{x}+{y}")
    
    def setup_year_selection(self, parent):
        """إعداد اختيار السنوات"""
        year_frame = tk.LabelFrame(parent, text="📅 اختيار السنوات", font=get_font('subtitle'))
        year_frame.pack(fill='x', pady=(0, 20))
        
        # السنة المصدر
        tk.Label(year_frame, text="السنة المصدر:", font=get_font('normal')).grid(row=0, column=0, sticky='w', padx=10, pady=5)
        
        self.source_year = tk.StringVar(value=SCHOOL_SETTINGS['current_year'])
        source_combo = ttk.Combobox(
            year_frame,
            textvariable=self.source_year,
            values=SCHOOL_SETTINGS['academic_years'],
            state='readonly',
            width=15
        )
        source_combo.grid(row=0, column=1, padx=10, pady=5)
        
        # السنة الهدف
        tk.Label(year_frame, text="السنة الهدف:", font=get_font('normal')).grid(row=1, column=0, sticky='w', padx=10, pady=5)
        
        self.target_year = tk.StringVar()
        target_combo = ttk.Combobox(
            year_frame,
            textvariable=self.target_year,
            values=SCHOOL_SETTINGS['academic_years'],
            state='readonly',
            width=15
        )
        target_combo.grid(row=1, column=1, padx=10, pady=5)
    
    def setup_transfer_options(self, parent):
        """إعداد خيارات النقل"""
        options_frame = tk.LabelFrame(parent, text="⚙️ خيارات النقل", font=get_font('subtitle'))
        options_frame.pack(fill='x', pady=(0, 20))
        
        # متغيرات الخيارات
        self.reset_payments = tk.BooleanVar(value=True)
        self.keep_discounts = tk.BooleanVar(value=True)
        self.update_fees = tk.BooleanVar(value=True)
        self.transfer_debts = tk.BooleanVar(value=True)
        self.upgrade_grades = tk.BooleanVar(value=True)
        
        # خيارات النقل
        options = [
            (self.reset_payments, "🔄 إعادة تصفير المدفوعات", "إعادة تصفير جميع المدفوعات والأقساط"),
            (self.keep_discounts, "🏷️ الاحتفاظ بالخصومات", "الاحتفاظ بخصومات الطلاب الحالية"),
            (self.update_fees, "💰 تحديث الرسوم", "تحديث الرسوم حسب المرحلة الجديدة"),
            (self.transfer_debts, "📋 نقل المديونيات", "نقل المبالغ المتبقية كمديونيات سابقة"),
            (self.upgrade_grades, "📈 ترقية المراحل", "ترقية الطلاب للمرحلة والفصل التالي")
        ]
        
        for i, (var, text, description) in enumerate(options):
            frame = tk.Frame(options_frame)
            frame.pack(fill='x', padx=10, pady=5)
            
            checkbox = tk.Checkbutton(
                frame,
                variable=var,
                text=text,
                font=get_font('normal')
            )
            checkbox.pack(anchor='w')
            
            desc_label = tk.Label(
                frame,
                text=description,
                font=get_font('small'),
                fg='gray'
            )
            desc_label.pack(anchor='w', padx=20)
    
    def setup_control_buttons(self, parent):
        """إعداد أزرار التحكم"""
        buttons_frame = tk.Frame(parent)
        buttons_frame.pack(fill='x', pady=20)
        
        # زر المعاينة
        preview_btn = tk.Button(
            buttons_frame,
            text="👁️ معاينة التغييرات",
            command=self.preview_transfer,
            bg=get_color('info'),
            fg='white',
            font=get_font('normal'),
            padx=20,
            pady=10
        )
        preview_btn.pack(side='left', padx=(0, 10))
        
        # زر التنفيذ
        execute_btn = tk.Button(
            buttons_frame,
            text="✅ تنفيذ النقل",
            command=self.execute_transfer,
            bg=get_color('success'),
            fg='white',
            font=get_font('normal'),
            padx=20,
            pady=10
        )
        execute_btn.pack(side='left', padx=10)
        
        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.cancel,
            bg=get_color('warning'),
            fg='white',
            font=get_font('normal'),
            padx=20,
            pady=10
        )
        cancel_btn.pack(side='right')
    
    def preview_transfer(self):
        """معاينة النقل"""
        if not self.validate_input():
            return
        
        options = self.get_transfer_options()
        
        # عرض رسالة تحميل
        messagebox.showinfo("معاينة", "سيتم عرض معاينة التغييرات قريباً...")
    
    def execute_transfer(self):
        """تنفيذ النقل"""
        if not self.validate_input():
            return
        
        # تأكيد العملية
        if not messagebox.askyesno(
            "تأكيد النقل",
            f"هل تريد نقل البيانات من {self.source_year.get()} إلى {self.target_year.get()}؟\n\n"
            "تحذير: هذه العملية لا يمكن التراجع عنها!"
        ):
            return
        
        options = self.get_transfer_options()
        
        # تنفيذ النقل
        success = self.transfer_manager.transfer_students(
            self.source_year.get(),
            self.target_year.get(),
            options
        )
        
        if success:
            self.result = True
            self.dialog.destroy()
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = False
        self.dialog.destroy()
    
    def validate_input(self) -> bool:
        """التحقق من صحة المدخلات"""
        if not self.source_year.get():
            messagebox.showerror("خطأ", "يرجى اختيار السنة المصدر")
            return False
        
        if not self.target_year.get():
            messagebox.showerror("خطأ", "يرجى اختيار السنة الهدف")
            return False
        
        if self.source_year.get() == self.target_year.get():
            messagebox.showerror("خطأ", "السنة المصدر والهدف لا يمكن أن تكونا متشابهتين")
            return False
        
        return True
    
    def get_transfer_options(self) -> Dict:
        """الحصول على خيارات النقل"""
        return {
            'reset_payments': self.reset_payments.get(),
            'keep_discounts': self.keep_discounts.get(),
            'update_fees': self.update_fees.get(),
            'transfer_debts': self.transfer_debts.get(),
            'upgrade_grades': self.upgrade_grades.get()
        }
    
    def show(self) -> bool:
        """عرض النافذة وانتظار النتيجة"""
        if self.parent:
            self.dialog.transient(self.parent)
            self.dialog.grab_set()
        self.dialog.wait_window()
        return self.result

class TransferPreviewDialog:
    """نافذة معاينة التغييرات"""
    
    def __init__(self, parent, preview_data: List[Dict]):
        self.parent = parent
        self.preview_data = preview_data
        self.result = False
        
        self.dialog = tk.Toplevel(parent) if parent else tk.Tk()
        self.setup_dialog()
    
    def setup_dialog(self):
        """إعداد نافذة المعاينة"""
        self.dialog.title("👁️ معاينة التغييرات")
        self.dialog.geometry("800x600")
        
        # إطار العنوان
        title_label = tk.Label(
            self.dialog,
            text="👁️ معاينة التغييرات قبل النقل",
            font=get_font('title'),
            bg=get_color('primary'),
            fg='white',
            pady=10
        )
        title_label.pack(fill='x')
        
        # جدول المعاينة
        self.setup_preview_table()
        
        # أزرار التحكم
        buttons_frame = tk.Frame(self.dialog)
        buttons_frame.pack(pady=20)
        
        tk.Button(
            buttons_frame,
            text="✅ تأكيد النقل",
            command=self.confirm,
            bg=get_color('success'),
            fg='white',
            font=get_font('normal'),
            padx=20
        ).pack(side='left', padx=10)
        
        tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.cancel,
            bg=get_color('warning'),
            fg='white',
            font=get_font('normal'),
            padx=20
        ).pack(side='left', padx=10)
    
    def setup_preview_table(self):
        """إعداد جدول المعاينة"""
        # إطار الجدول
        table_frame = tk.Frame(self.dialog)
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # إنشاء Treeview
        columns = ('name', 'old_stage', 'new_stage', 'old_fees', 'new_fees', 'changes')
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تحديد رؤوس الأعمدة
        headers = {
            'name': 'اسم الطالب',
            'old_stage': 'المرحلة السابقة',
            'new_stage': 'المرحلة الجديدة',
            'old_fees': 'الرسوم السابقة',
            'new_fees': 'الرسوم الجديدة',
            'changes': 'التغييرات'
        }
        
        for col, header in headers.items():
            self.tree.heading(col, text=header)
            self.tree.column(col, width=120, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # ترتيب العناصر
        self.tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # تحميل البيانات
        self.load_preview_data()
    
    def load_preview_data(self):
        """تحميل بيانات المعاينة"""
        for item in self.preview_data:
            original = item['original']
            new = item['new']
            changes = item['changes']
            
            self.tree.insert('', 'end', values=(
                original.get('name', ''),
                f"{original.get('stage', '')} - {original.get('class', '')}",
                f"{new.get('stage', '')} - {new.get('class', '')}",
                f"{float(original.get('total_fees', 0)):,.2f}",
                f"{float(new.get('total_fees', 0)):,.2f}",
                '; '.join(changes[:2])  # أول تغييرين فقط
            ))
    
    def confirm(self):
        """تأكيد النقل"""
        self.result = True
        self.dialog.destroy()
    
    def cancel(self):
        """إلغاء النقل"""
        self.result = False
        self.dialog.destroy()
    
    def show(self) -> bool:
        """عرض النافذة وانتظار النتيجة"""
        if self.parent:
            self.dialog.transient(self.parent)
            self.dialog.grab_set()
        self.dialog.wait_window()
        return self.result
